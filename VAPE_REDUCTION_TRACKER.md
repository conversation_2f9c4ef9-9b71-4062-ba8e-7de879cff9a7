# Vape Reduction Tracker Feature Documentation

## Overview

The Vape Reduction Tracker is a comprehensive feature that helps users gradually decrease their daily vaping sessions over a configurable time period (30, 60, or 90 days). This feature provides an alternative to the existing break timer system, offering a more structured approach to vaping cessation.

## Key Features

### 1. Dual Tracking Modes

#### Break Timer Mode (Legacy)
- Traditional countdown timer between vaping breaks
- Fixed number of breaks per day
- Timer-based intervals

#### Session Tracking Mode (New)
- Log individual vaping sessions
- Gradual reduction over time
- Smart pace monitoring

### 2. Initial Setup & Configuration

#### Onboarding Flow
1. **Tracking Mode Selection**: Choose between Break Timer or Session Tracking
2. **Daily Vaping Count**: Input current baseline vaping sessions per day
3. **Sleep Schedule**: Configure sleep duration (4-12 hours, default: 6)
4. **Reduction Timeline**: Select cessation speed (30, 60, or 90 days)

#### Automatic Calculations
- **Active Hours**: 24 - sleep duration (default: 18 hours)
- **Daily Allowance**: Gradually decreases based on reduction timeline
- **Target Rate**: Sessions per hour for optimal pacing

### 3. Visual Progress Components

#### Circular Progress Indicator
- **Real-time display**: Shows sessions logged vs daily allowance
- **Color-coded status**: Green (good), yellow (caution), orange (warning), red (exceeded)
- **Smooth animations**: CSS transitions for progress updates
- **Mobile responsive**: Scales appropriately for different screen sizes

#### Session Logging Interface
- **Manual input**: Number field for logging multiple sessions
- **Quick-add buttons**: +1, +3, +5 for common patterns
- **Timestamp tracking**: Records exact time of each session
- **Undo functionality**: Remove accidentally logged sessions

### 4. Smart Warning System

#### Pace Checking Algorithm
- **Time-aware calculations**: Considers active hours and current time
- **Contextual warnings**: Different messages for morning, midday, afternoon, evening
- **Multiple severity levels**: Great, good, caution, warning, exceeded

#### Pattern Recognition
- **Rapid succession detection**: Warns about 3+ sessions in 30 minutes
- **High frequency alerts**: Notices 5+ sessions in 2 hours
- **Batch logging detection**: Suggests real-time logging

#### Motivational Milestones
- **Progress celebrations**: 25%, 50%, 75%, 90% milestones
- **One-time notifications**: Each milestone shows only once per day
- **Encouraging messages**: Positive reinforcement for good pacing

### 5. Enhanced Statistics & Settings

#### Statistics Page
- **Mode-specific metrics**: Different stats for break vs reduction modes
- **Reduction progress**: Overall journey completion percentage
- **Weekly averages**: Sessions per day trending
- **Best adherence tracking**: Days with best session control

#### Settings Page
- **Sleep duration control**: Adjustable sleep hours
- **Warning sensitivity**: Low/Normal/High frequency options
- **Timeline adjustments**: Change reduction speed
- **Mode switching**: Toggle between tracking methods

## Technical Implementation

### Data Structure

```javascript
userData = {
  // Mode selection
  trackingMode: 'reduction', // 'breaks' or 'reduction'
  
  // Reduction tracking fields
  baselineVapeCount: 20,      // Original daily count
  reductionTimeline: 60,      // Days for reduction
  sleepDuration: 6,           // Hours of sleep
  activeHours: 18,            // Calculated active hours
  
  // Daily tracking
  dailyAllowance: 18,         // Current day's allowance
  sessionsLogged: 5,          // Sessions logged today
  sessionTimes: [...],        // Array of timestamps
  lastSessionTime: '...',     // Last session time
  warningThreshold: 0.8       // Warning sensitivity
}
```

### Core Algorithms

#### Daily Allowance Calculation
```javascript
function calculateDailyAllowance(currentDay, baselineCount, totalDays) {
  const reductionFactor = currentDay / totalDays;
  return Math.max(1, Math.round(baselineCount * (1 - reductionFactor)));
}
```

#### Pace Checking
```javascript
function checkConsumptionPace(sessionsLogged, dailyAllowance, hoursElapsed, activeHours) {
  const timePercentageElapsed = hoursElapsed / activeHours;
  const expectedSessions = timePercentageElapsed * dailyAllowance;
  const usagePercentage = (sessionsLogged / dailyAllowance) * 100;
  
  // Return status and contextual message
}
```

### Data Persistence

#### Enhanced localStorage Handling
- **Data validation**: Validates all fields before saving
- **Automatic backups**: Creates backups before each save
- **Error recovery**: Restores from backup if save fails
- **Data cleanup**: Removes history older than 90 days

#### Daily Reset Logic
- **Timezone handling**: Uses both local and ISO dates
- **Data integrity checks**: Validates dates and prevents corruption
- **Corrective reset**: Automatically fixes invalid data
- **Edge case handling**: Handles time travel and large jumps

## User Experience

### Workflow
1. **Setup**: Complete onboarding with tracking mode and preferences
2. **Daily Use**: Log vaping sessions throughout the day
3. **Monitoring**: View progress and receive contextual feedback
4. **Adjustment**: Modify settings as needed for optimal experience

### Visual Feedback
- **Progress ring**: Shows daily consumption vs allowance
- **Status messages**: Contextual warnings and encouragement
- **Day counter**: Displays current day and journey progress
- **Statistics**: Weekly trends and milestone achievements

### Mobile Optimization
- **Touch-friendly**: Large buttons and proper spacing
- **Responsive design**: Adapts to different screen sizes
- **Portrait orientation**: Optimized for mobile usage
- **Smooth animations**: 60fps transitions and updates

## Benefits

### For Users
- **Gradual reduction**: Sustainable approach to quitting
- **Smart guidance**: Contextual warnings and encouragement
- **Progress tracking**: Visual feedback on journey progress
- **Flexibility**: Customizable timelines and sensitivity

### For Developers
- **Modular design**: Clean separation between tracking modes
- **Extensible**: Easy to add new features and tracking methods
- **Robust**: Comprehensive error handling and data validation
- **Maintainable**: Well-documented code with clear structure

## Future Enhancements

### Potential Features
- **Social sharing**: Share milestones with friends
- **Health integration**: Connect with fitness apps
- **Advanced analytics**: Detailed pattern analysis
- **Customizable goals**: Non-linear reduction curves
- **Reminder system**: Smart notifications for pacing

### Technical Improvements
- **Cloud sync**: Backup data across devices
- **Offline support**: Full functionality without internet
- **Performance optimization**: Faster load times
- **Accessibility**: Enhanced screen reader support
