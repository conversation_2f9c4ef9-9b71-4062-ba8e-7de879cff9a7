/**
 * ByeVape App - Main Application Logic
 * Handles navigation, routing, and core app functionality
 */

// Capacitor plugins (optional for browser development)
let SplashScreen = null;
try {
  // Only import Capacitor plugins if available (mobile environment)
  if (typeof window !== 'undefined' && window.Capacitor) {
    import('@capacitor/splash-screen').then(module => {
      SplashScreen = module.SplashScreen;
    }).catch(err => {
      console.log('Capacitor SplashScreen not available in browser environment');
    });
  }
} catch (error) {
  console.log('Capacitor not available, running in browser mode');
}

// ===================================
// APP STATE MANAGEMENT
// ===================================

class AppState {
  constructor() {
    this.currentPage = 'tracker';
    this.isFirstLaunch = true;
    this.currentOnboardingStep = 1; // Track current onboarding step
    this.userData = {
      // Existing fields (for backward compatibility)
      dailyVapingCount: 0,
      cessationSpeed: 'normal',
      startDate: null,
      currentDay: 1,
      breaksRemaining: 0,
      totalBreaks: 0,
      breaksTaken: 0,

      // New fields for reduction tracking
      trackingMode: 'breaks', // 'breaks' or 'reduction'
      baselineVapeCount: 0, // Original daily vaping count for reduction mode
      reductionTimeline: 60, // Days for reduction (30, 60, or 90)
      sleepDuration: 6, // Hours of sleep per day
      activeHours: 18, // Calculated: 24 - sleepDuration

      // Universal puff tracking fields
      totalPuffsToday: 0, // Total puffs taken today
      lastRecordedDevicePuffValue: 0, // Last device puff counter value
      puffTimes: [], // Array of puff timestamps
      lastPuffTime: null, // ISO string of last puff time
      dailyPuffTarget: 0, // Target puffs for today (calculated)
      averagePuffsPerBreak: 10, // Average puffs per vaping break

      // Reduction mode specific data
      dailyAllowance: 0, // Current day's allowed vape sessions
      sessionsLogged: 0, // Sessions logged today
      sessionTimes: [], // Array of session timestamps
      lastSessionTime: null, // Last session timestamp
      warningThreshold: 0.8 // Warning when 80% of daily allowance used
    };
    this.dailyHistory = {}; // Store daily statistics
    this.init();
  }

  init() {
    try {
      console.log('Initializing ByeVape AppState...');
      this.loadUserData();
      this.checkFirstLaunch();
      this.initializeNavigation();
      this.startApp();
      console.log('ByeVape AppState initialized successfully');
    } catch (error) {
      console.error('Error during AppState initialization:', error);
      // Show a user-friendly error message
      this.showAlert('App initialization failed. Please refresh the page.', 'error');
    }
  }

  loadUserData() {
    try {
      const savedData = localStorage.getItem('byevape-userdata');
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        this.userData = { ...this.userData, ...parsedData };

        // Data migration for existing users
        this.migrateUserData();

        this.isFirstLaunch = false;
        console.log('User data loaded successfully');
      } else {
        console.log('No saved user data found - first launch');
      }

      // Load daily history
      const savedHistory = localStorage.getItem('byevape-daily-history');
      if (savedHistory) {
        this.dailyHistory = JSON.parse(savedHistory);
        console.log('Daily history loaded:', Object.keys(this.dailyHistory).length, 'days');
      } else {
        console.log('No daily history found');
      }
    } catch (error) {
      console.error('Error loading user data:', error);
      // Reset to default state if data is corrupted
      this.resetToDefaultUserData();
      this.dailyHistory = {};
    }
  }

  // Data migration for backward compatibility
  migrateUserData() {
    let migrationNeeded = false;

    // Migrate tracking mode (default to 'breaks' for existing users)
    if (!this.userData.trackingMode) {
      this.userData.trackingMode = 'breaks';
      migrationNeeded = true;
    }

    // Set baseline vape count from existing dailyVapingCount if not set
    if (!this.userData.baselineVapeCount && this.userData.dailyVapingCount) {
      this.userData.baselineVapeCount = this.userData.dailyVapingCount;
      migrationNeeded = true;
    }

    // Set default reduction timeline based on cessation speed
    if (!this.userData.reductionTimeline) {
      const timelineMap = { 'fast': 30, 'normal': 60, 'relaxed': 90 };
      this.userData.reductionTimeline = timelineMap[this.userData.cessationSpeed] || 60;
      migrationNeeded = true;
    }

    // Set default sleep duration and calculate active hours
    if (!this.userData.sleepDuration) {
      this.userData.sleepDuration = 6;
      this.userData.activeHours = 18;
      migrationNeeded = true;
    }

    // Initialize session tracking arrays if not present
    if (!this.userData.sessionTimes) {
      this.userData.sessionTimes = [];
      this.userData.sessionsLogged = 0;
      this.userData.lastSessionTime = null;
      migrationNeeded = true;
    }

    // Set default warning threshold
    if (!this.userData.warningThreshold) {
      this.userData.warningThreshold = 0.8;
      migrationNeeded = true;
    }

    // Initialize puff tracking fields if not present
    if (this.userData.totalPuffsToday === undefined) {
      this.userData.totalPuffsToday = 0;
      this.userData.lastRecordedDevicePuffValue = 0;
      this.userData.puffTimes = [];
      this.userData.lastPuffTime = null;
      this.userData.dailyPuffTarget = 0;
      this.userData.averagePuffsPerBreak = 10;
      migrationNeeded = true;
    }

    if (migrationNeeded) {
      console.log('🔄 User data migrated to new format');
      this.saveUserData();
    }
  }

  // Reset to default user data structure
  resetToDefaultUserData() {
    this.userData = {
      // Existing fields
      dailyVapingCount: 0,
      cessationSpeed: 'normal',
      startDate: null,
      currentDay: 1,
      breaksRemaining: 0,
      totalBreaks: 0,
      breaksTaken: 0,

      // New fields
      trackingMode: 'breaks',
      baselineVapeCount: 0,
      reductionTimeline: 60,
      sleepDuration: 6,
      activeHours: 18,

      // Universal puff tracking fields
      totalPuffsToday: 0,
      lastRecordedDevicePuffValue: 0,
      puffTimes: [],
      lastPuffTime: null,
      dailyPuffTarget: 0,
      averagePuffsPerBreak: 10,

      // Legacy fields (for backward compatibility)
      dailyAllowance: 0,
      sessionsLogged: 0,
      sessionTimes: [],
      lastSessionTime: null,
      warningThreshold: 0.8
    };
  }

  saveUserData() {
    try {
      // Validate data before saving
      if (!this.validateUserData()) {
        console.error('❌ Invalid user data, not saving');
        return false;
      }

      // Create backup before saving
      this.createDataBackup();

      localStorage.setItem('byevape-userdata', JSON.stringify(this.userData));
      console.log('✅ User data saved to localStorage');
      return true;
    } catch (error) {
      console.error('❌ Error saving user data:', error);

      // Try to restore from backup
      this.restoreFromBackup();

      this.showAlert('Error saving data. Please check your storage space.', 'error');
      return false;
    }
  }

  saveDailyHistory() {
    try {
      // Clean old history data (keep only last 90 days)
      this.cleanOldHistoryData();

      localStorage.setItem('byevape-daily-history', JSON.stringify(this.dailyHistory));
      console.log('✅ Daily history saved to localStorage');
      return true;
    } catch (error) {
      console.error('❌ Error saving daily history:', error);
      this.showAlert('Error saving daily history.', 'error');
      return false;
    }
  }

  validateUserData() {
    // Check required fields
    const requiredFields = ['trackingMode', 'currentDay', 'startDate'];
    for (const field of requiredFields) {
      if (this.userData[field] === undefined || this.userData[field] === null) {
        console.error(`❌ Missing required field: ${field}`);
        return false;
      }
    }

    // Validate tracking mode specific data
    if (this.userData.trackingMode === 'reduction') {
      if (this.userData.baselineVapeCount <= 0 || this.userData.reductionTimeline <= 0) {
        console.error('❌ Invalid reduction tracking data');
        return false;
      }
    } else if (this.userData.trackingMode === 'breaks') {
      if (this.userData.totalBreaks <= 0) {
        console.error('❌ Invalid break tracking data');
        return false;
      }
    }

    // Validate numeric ranges
    if (this.userData.currentDay < 1 || this.userData.currentDay > 365) {
      console.error('❌ Invalid current day value');
      return false;
    }

    return true;
  }

  createDataBackup() {
    try {
      const backupData = {
        userData: { ...this.userData },
        dailyHistory: { ...this.dailyHistory },
        timestamp: new Date().toISOString()
      };

      localStorage.setItem('byevape-backup', JSON.stringify(backupData));
      console.log('📦 Data backup created');
    } catch (error) {
      console.error('❌ Error creating backup:', error);
    }
  }

  restoreFromBackup() {
    try {
      const backupData = localStorage.getItem('byevape-backup');
      if (backupData) {
        const backup = JSON.parse(backupData);
        this.userData = backup.userData;
        this.dailyHistory = backup.dailyHistory;
        console.log('🔄 Data restored from backup');
        return true;
      }
    } catch (error) {
      console.error('❌ Error restoring from backup:', error);
    }
    return false;
  }

  cleanOldHistoryData() {
    const today = new Date();
    const ninetyDaysAgo = new Date(today.getTime() - (90 * 24 * 60 * 60 * 1000));
    const cutoffDate = ninetyDaysAgo.toISOString().split('T')[0];

    let cleanedCount = 0;
    Object.keys(this.dailyHistory).forEach(dateStr => {
      if (dateStr < cutoffDate) {
        delete this.dailyHistory[dateStr];
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned ${cleanedCount} old history entries`);
    }
  }

  recordDailyStats() {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

    // Record stats based on tracking mode with universal puff tracking
    if (this.userData.trackingMode === 'reduction') {
      this.dailyHistory[today] = {
        date: today,
        trackingMode: 'reduction',
        dailyAllowance: this.userData.dailyAllowance,
        sessionsLogged: this.userData.sessionsLogged,
        totalPuffsToday: this.userData.totalPuffsToday,
        dailyPuffTarget: this.userData.dailyPuffTarget,
        currentDay: this.userData.currentDay,
        cessationSpeed: this.userData.cessationSpeed,
        reductionTimeline: this.userData.reductionTimeline,
        progressPercent: this.userData.dailyPuffTarget > 0
          ? ((this.userData.totalPuffsToday / this.userData.dailyPuffTarget) * 100)
          : 0,
        reductionPercent: this.calculateReductionProgress()
      };
    } else {
      // Legacy break mode with puff tracking
      this.dailyHistory[today] = {
        date: today,
        trackingMode: 'breaks',
        totalBreaks: this.userData.totalBreaks,
        breaksTaken: this.userData.breaksTaken,
        breaksRemaining: this.userData.breaksRemaining,
        totalPuffsToday: this.userData.totalPuffsToday,
        dailyPuffTarget: this.userData.dailyPuffTarget,
        currentDay: this.userData.currentDay,
        cessationSpeed: this.userData.cessationSpeed,
        progressPercent: this.userData.totalBreaks > 0
          ? ((this.userData.breaksTaken / this.userData.totalBreaks) * 100)
          : 0
      };
    }

    this.saveDailyHistory();
    console.log(`📊 Daily stats recorded for ${today} - Puffs: ${this.userData.totalPuffsToday}/${this.userData.dailyPuffTarget}`);
  }

  // ===================================
  // REDUCTION TRACKING ALGORITHMS
  // ===================================

  // Calculate daily allowance based on reduction timeline
  calculateDailyAllowance(currentDay = null) {
    if (this.userData.trackingMode !== 'reduction') {
      return this.userData.dailyVapingCount; // Fallback for break mode
    }

    const day = currentDay || this.userData.currentDay;
    const baselineCount = this.userData.baselineVapeCount;
    const totalDays = this.userData.reductionTimeline;

    // Linear reduction: allowance = baseline * (1 - (currentDay / totalDays))
    const reductionFactor = Math.min(day / totalDays, 1); // Cap at 1 to prevent negative values
    const allowance = Math.max(1, Math.round(baselineCount * (1 - reductionFactor)));

    console.log(`📉 Day ${day}: Allowance = ${allowance} (${(reductionFactor * 100).toFixed(1)}% reduction)`);
    return allowance;
  }

  // Calculate reduction progress percentage
  calculateReductionProgress() {
    if (this.userData.trackingMode !== 'reduction') {
      return 0;
    }

    const currentDay = this.userData.currentDay;
    const totalDays = this.userData.reductionTimeline;
    return Math.min((currentDay / totalDays) * 100, 100);
  }

  // Enhanced consumption pace checking with smart warnings
  checkConsumptionPace() {
    if (this.userData.trackingMode !== 'reduction') {
      return { status: 'ok', message: null };
    }

    const now = new Date();
    const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 6, 0); // 6 AM start
    const dayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 24 - this.userData.sleepDuration, 0);

    // Handle edge cases for time calculations
    let hoursElapsed;
    if (now < dayStart) {
      // Before active hours start
      hoursElapsed = 0;
    } else if (now > dayEnd) {
      // After active hours end
      hoursElapsed = this.userData.activeHours;
    } else {
      // During active hours
      hoursElapsed = (now - dayStart) / (1000 * 60 * 60);
    }

    const activeHours = this.userData.activeHours;
    const timePercentageElapsed = Math.min(hoursElapsed / activeHours, 1);
    const expectedSessions = timePercentageElapsed * this.userData.dailyAllowance;
    const actualSessions = this.userData.sessionsLogged;

    // Calculate usage percentage
    const usagePercentage = this.userData.dailyAllowance > 0
      ? (actualSessions / this.userData.dailyAllowance) * 100
      : 0;

    // Calculate sessions per hour rate
    const currentRate = hoursElapsed > 0 ? actualSessions / hoursElapsed : 0;
    const targetRate = this.userData.dailyAllowance / activeHours;

    console.log(`⏰ Pace check: ${hoursElapsed.toFixed(1)}h elapsed (${(timePercentageElapsed * 100).toFixed(1)}%), expected: ${expectedSessions.toFixed(1)}, actual: ${actualSessions}`);
    console.log(`📊 Rate: ${currentRate.toFixed(2)}/h (target: ${targetRate.toFixed(2)}/h), usage: ${usagePercentage.toFixed(1)}%`);

    return this.generateSmartWarning(actualSessions, expectedSessions, usagePercentage, hoursElapsed, currentRate, targetRate, timePercentageElapsed);
  }

  // Enhanced puff consumption pace checking
  checkPuffConsumptionPace() {
    const now = new Date();
    const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 6, 0); // 6 AM start
    const dayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 24 - this.userData.sleepDuration, 0);

    // Handle edge cases for time calculations
    let hoursElapsed;
    if (now < dayStart) {
      hoursElapsed = 0;
    } else if (now > dayEnd) {
      hoursElapsed = this.userData.activeHours;
    } else {
      hoursElapsed = (now - dayStart) / (1000 * 60 * 60);
    }

    const activeHours = this.userData.activeHours;
    const timePercentageElapsed = Math.min(hoursElapsed / activeHours, 1);
    const expectedPuffs = timePercentageElapsed * this.userData.dailyPuffTarget;
    const actualPuffs = this.userData.totalPuffsToday;

    // Calculate usage percentage
    const usagePercentage = this.userData.dailyPuffTarget > 0
      ? (actualPuffs / this.userData.dailyPuffTarget) * 100
      : 0;

    // Calculate puffs per hour rate
    const currentRate = hoursElapsed > 0 ? actualPuffs / hoursElapsed : 0;
    const targetRate = this.userData.dailyPuffTarget / activeHours;

    console.log(`⏰ Puff pace check: ${hoursElapsed.toFixed(1)}h elapsed, expected: ${expectedPuffs.toFixed(1)}, actual: ${actualPuffs}`);
    console.log(`📊 Puff rate: ${currentRate.toFixed(2)}/h (target: ${targetRate.toFixed(2)}/h), usage: ${usagePercentage.toFixed(1)}%`);

    return this.generatePuffWarning(actualPuffs, expectedPuffs, usagePercentage, hoursElapsed, currentRate, targetRate, timePercentageElapsed);
  }

  generatePuffWarning(actualPuffs, expectedPuffs, usagePercentage, hoursElapsed, currentRate, targetRate, timePercentageElapsed) {
    const remainingPuffs = this.userData.dailyPuffTarget - actualPuffs;
    const remainingHours = this.userData.activeHours - hoursElapsed;

    // No puffs logged yet
    if (actualPuffs === 0) {
      if (hoursElapsed > 4) {
        return {
          status: 'great',
          message: `🎉 Excellent! You've gone ${hoursElapsed.toFixed(1)} hours without any puffs today.`
        };
      } else {
        return {
          status: 'great',
          message: 'Great start! You haven\'t taken any puffs yet today.'
        };
      }
    }

    // Exceeded daily target
    if (usagePercentage >= 100) {
      const excess = actualPuffs - this.userData.dailyPuffTarget;
      return {
        status: 'exceeded',
        message: `🚨 You've exceeded your daily puff target by ${excess} puffs. Consider taking a break.`
      };
    }

    // Time-based contextual warnings for puffs
    const timeOfDay = this.getTimeOfDayContext(hoursElapsed);

    // Significantly ahead of pace (>50% ahead)
    if (actualPuffs > expectedPuffs * 1.5) {
      return {
        status: 'warning',
        message: this.getPuffContextualWarning('high', timeOfDay, usagePercentage, remainingPuffs, remainingHours)
      };
    }

    // Moderately ahead of pace (20-50% ahead)
    if (actualPuffs > expectedPuffs * 1.2) {
      return {
        status: 'warning',
        message: this.getPuffContextualWarning('medium', timeOfDay, usagePercentage, remainingPuffs, remainingHours)
      };
    }

    // Slightly ahead of pace (10-20% ahead)
    if (actualPuffs > expectedPuffs * 1.1) {
      return {
        status: 'caution',
        message: this.getPuffContextualWarning('low', timeOfDay, usagePercentage, remainingPuffs, remainingHours)
      };
    }

    // Behind pace (positive reinforcement)
    if (actualPuffs < expectedPuffs * 0.8) {
      return {
        status: 'great',
        message: this.getPuffPositiveReinforcement(timeOfDay, usagePercentage, remainingPuffs)
      };
    }

    // On track
    return {
      status: 'good',
      message: this.getPuffOnTrackMessage(timeOfDay, usagePercentage, remainingPuffs)
    };
  }

  getPuffContextualWarning(severity, timeOfDay, usagePercentage, remainingPuffs, remainingHours) {
    const messages = {
      high: {
        morning: `⚠️ You've taken ${usagePercentage.toFixed(0)}% of your daily puffs before midday. Consider slowing down.`,
        midday: `⚠️ You're well ahead of your puff pace. ${remainingPuffs} puffs left for ${remainingHours.toFixed(1)} hours.`,
        afternoon: `⚠️ Heavy puff usage today. Try to pace yourself for the rest of the day.`,
        evening: `⚠️ You've taken most of your daily puffs. Consider winding down for the day.`
      },
      medium: {
        morning: `💡 You're ahead of your morning puff target. ${remainingPuffs} puffs remaining.`,
        midday: `💡 Moderate puff pace today. ${remainingPuffs} puffs left for ${remainingHours.toFixed(1)} hours.`,
        afternoon: `💡 You're slightly ahead of your puff pace. ${usagePercentage.toFixed(0)}% of daily target reached.`,
        evening: `💡 Good puff pacing today. ${remainingPuffs} puffs remaining.`
      },
      low: {
        morning: `ℹ️ Slightly ahead of morning puff pace. ${remainingPuffs} puffs remaining today.`,
        midday: `ℹ️ You're on a good puff pace. ${usagePercentage.toFixed(0)}% of target reached.`,
        afternoon: `ℹ️ Steady puff progress. ${remainingPuffs} puffs left for today.`,
        evening: `ℹ️ Good puff control today. ${remainingPuffs} puffs remaining.`
      }
    };

    return messages[severity][timeOfDay];
  }

  getPuffPositiveReinforcement(timeOfDay, usagePercentage, remainingPuffs) {
    const messages = {
      morning: `🌟 Excellent puff control this morning! ${remainingPuffs} puffs available.`,
      midday: `🌟 Great puff pacing! You're doing better than expected.`,
      afternoon: `🌟 Outstanding puff control today! ${usagePercentage.toFixed(0)}% of target used.`,
      evening: `🌟 Fantastic day! You've shown great puff restraint.`
    };

    return messages[timeOfDay];
  }

  getPuffOnTrackMessage(timeOfDay, usagePercentage, remainingPuffs) {
    const messages = {
      morning: `✅ Perfect morning puff pace! ${remainingPuffs} puffs available.`,
      midday: `✅ Right on track! ${usagePercentage.toFixed(0)}% of puff target reached.`,
      afternoon: `✅ Steady puff progress! ${remainingPuffs} puffs remaining.`,
      evening: `✅ Well-balanced day! ${remainingPuffs} puffs left.`
    };

    return messages[timeOfDay];
  }

  generateSmartWarning(actualSessions, expectedSessions, usagePercentage, hoursElapsed, currentRate, targetRate, timePercentageElapsed) {
    const remainingSessions = this.userData.dailyAllowance - actualSessions;
    const remainingHours = this.userData.activeHours - hoursElapsed;

    // No sessions logged yet
    if (actualSessions === 0) {
      if (hoursElapsed > 4) {
        return {
          status: 'great',
          message: `🎉 Excellent! You've gone ${hoursElapsed.toFixed(1)} hours without vaping today.`
        };
      } else {
        return {
          status: 'great',
          message: 'Great start! You haven\'t vaped yet today.'
        };
      }
    }

    // Exceeded daily limit
    if (usagePercentage >= 100) {
      const hoursOverLimit = Math.max(0, hoursElapsed - this.userData.activeHours);
      if (hoursOverLimit > 0) {
        return {
          status: 'exceeded',
          message: `🚨 Daily limit reached ${hoursOverLimit.toFixed(1)}h ago. Tomorrow is a fresh start!`
        };
      } else {
        return {
          status: 'exceeded',
          message: '🚨 You\'ve reached your daily limit. Consider some breathing exercises or a walk.'
        };
      }
    }

    // Time-based contextual warnings
    const timeOfDay = this.getTimeOfDayContext(hoursElapsed);

    // Significantly ahead of pace (>50% ahead)
    if (actualSessions > expectedSessions * 1.5) {
      return {
        status: 'warning',
        message: this.getContextualWarning('high', timeOfDay, usagePercentage, remainingSessions, remainingHours)
      };
    }

    // Moderately ahead of pace (20-50% ahead)
    if (actualSessions > expectedSessions * 1.2) {
      return {
        status: 'warning',
        message: this.getContextualWarning('medium', timeOfDay, usagePercentage, remainingSessions, remainingHours)
      };
    }

    // Slightly ahead of pace (10-20% ahead)
    if (actualSessions > expectedSessions * 1.1) {
      return {
        status: 'caution',
        message: this.getContextualWarning('low', timeOfDay, usagePercentage, remainingSessions, remainingHours)
      };
    }

    // Behind pace (positive reinforcement)
    if (actualSessions < expectedSessions * 0.8) {
      return {
        status: 'great',
        message: this.getPositiveReinforcement(timeOfDay, usagePercentage, remainingSessions)
      };
    }

    // On track
    return {
      status: 'good',
      message: this.getOnTrackMessage(timeOfDay, usagePercentage, remainingSessions)
    };
  }

  getTimeOfDayContext(hoursElapsed) {
    if (hoursElapsed < 3) return 'morning';
    if (hoursElapsed < 8) return 'midday';
    if (hoursElapsed < 14) return 'afternoon';
    return 'evening';
  }

  getContextualWarning(severity, timeOfDay, usagePercentage, remainingSessions, remainingHours) {
    const messages = {
      high: {
        morning: `⚠️ You've used ${usagePercentage.toFixed(0)}% of your allowance before midday. Consider slowing down.`,
        midday: `⚠️ You're well ahead of pace. ${remainingSessions} sessions left for ${remainingHours.toFixed(1)} hours.`,
        afternoon: `⚠️ Heavy usage today. Try to pace yourself for the rest of the day.`,
        evening: `⚠️ You've used most of your allowance. Consider winding down for the day.`
      },
      medium: {
        morning: `💡 You're ahead of your morning target. ${remainingSessions} sessions remaining.`,
        midday: `💡 Moderate pace today. ${remainingSessions} sessions left for ${remainingHours.toFixed(1)} hours.`,
        afternoon: `💡 You're slightly ahead of pace. ${usagePercentage.toFixed(0)}% of daily allowance used.`,
        evening: `💡 Good pacing today. ${remainingSessions} sessions remaining.`
      },
      low: {
        morning: `ℹ️ Slightly ahead of morning pace. ${remainingSessions} sessions remaining today.`,
        midday: `ℹ️ You're on a good pace. ${usagePercentage.toFixed(0)}% of allowance used.`,
        afternoon: `ℹ️ Steady progress. ${remainingSessions} sessions left for today.`,
        evening: `ℹ️ Good control today. ${remainingSessions} sessions remaining.`
      }
    };

    return messages[severity][timeOfDay];
  }

  getPositiveReinforcement(timeOfDay, usagePercentage, remainingSessions) {
    const messages = {
      morning: `🌟 Excellent self-control this morning! ${remainingSessions} sessions available.`,
      midday: `🌟 Great pacing! You're doing better than expected.`,
      afternoon: `🌟 Outstanding control today! ${usagePercentage.toFixed(0)}% used so far.`,
      evening: `🌟 Fantastic day! You've shown great restraint.`
    };

    return messages[timeOfDay];
  }

  getOnTrackMessage(timeOfDay, usagePercentage, remainingSessions) {
    const messages = {
      morning: `✅ Perfect morning pace! ${remainingSessions} sessions available.`,
      midday: `✅ Right on track! ${usagePercentage.toFixed(0)}% of allowance used.`,
      afternoon: `✅ Steady progress! ${remainingSessions} sessions remaining.`,
      evening: `✅ Well-balanced day! ${remainingSessions} sessions left.`
    };

    return messages[timeOfDay];
  }

  // Log individual puffs with enhanced tracking
  logPuffs(count = 1) {
    const now = new Date();

    // Add puff timestamps
    for (let i = 0; i < count; i++) {
      this.userData.puffTimes.push(now.toISOString());
    }

    this.userData.totalPuffsToday += count;
    this.userData.lastPuffTime = now.toISOString();

    // For backward compatibility, also update session data
    if (this.userData.trackingMode === 'reduction') {
      this.userData.sessionsLogged += Math.ceil(count / this.userData.averagePuffsPerBreak);
      this.userData.lastSessionTime = now.toISOString();
    }

    // Save data
    this.saveUserData();
    this.recordDailyStats();

    console.log(`📝 Logged ${count} puff(s). Total today: ${this.userData.totalPuffsToday}/${this.userData.dailyPuffTarget || 'unlimited'}`);

    // Enhanced warning system with smart timing
    this.processPuffWarnings(count);

    return true;
  }

  // Log puffs from device counter (calculates delta)
  logDevicePuffs(devicePuffValue) {
    if (devicePuffValue <= this.userData.lastRecordedDevicePuffValue) {
      console.log('⚠️ Device puff value must be higher than last recorded value');
      return false;
    }

    const puffDelta = devicePuffValue - this.userData.lastRecordedDevicePuffValue;
    this.userData.lastRecordedDevicePuffValue = devicePuffValue;

    console.log(`📱 Device sync: ${puffDelta} new puffs (device total: ${devicePuffValue})`);

    return this.logPuffs(puffDelta);
  }

  // Calculate daily puff target based on tracking mode and current day
  calculateDailyPuffTarget() {
    if (this.userData.trackingMode === 'breaks') {
      // Break mode: target = total breaks * average puffs per break
      return this.userData.totalBreaks * this.userData.averagePuffsPerBreak;
    } else if (this.userData.trackingMode === 'reduction') {
      // Reduction mode: target = daily allowance * average puffs per session
      return this.userData.dailyAllowance * this.userData.averagePuffsPerBreak;
    }
    return 0;
  }

  // Update daily puff target and save
  updateDailyPuffTarget() {
    this.userData.dailyPuffTarget = this.calculateDailyPuffTarget();
    console.log(`🎯 Daily puff target updated: ${this.userData.dailyPuffTarget} puffs`);
  }

  // Enhanced puff-based warning system
  processPuffWarnings(puffsAdded) {
    const paceCheck = this.checkPuffConsumptionPace();

    if (paceCheck.status !== 'ok') {
      this.showStatusMessage(paceCheck.message, paceCheck.status);
    }

    // Check if exceeded daily target
    if (this.userData.dailyPuffTarget > 0 && this.userData.totalPuffsToday > this.userData.dailyPuffTarget) {
      const excess = this.userData.totalPuffsToday - this.userData.dailyPuffTarget;
      this.showStatusMessage(`⚠️ You've exceeded your daily target by ${excess} puffs. Consider taking a break.`, 'warning');
    }
  }

  processSessionWarnings(sessionCount) {
    const paceCheck = this.checkConsumptionPace();

    // Always show status message for visual feedback
    if (paceCheck.message) {
      this.showAlert(paceCheck.message, this.getAlertType(paceCheck.status));
    }

    // Additional warnings for concerning patterns
    this.checkSessionPatterns(sessionCount);

    // Motivational milestones
    this.checkMotivationalMilestones();
  }

  checkSessionPatterns(sessionCount) {
    const recentSessions = this.getRecentSessionPattern();

    // Rapid succession warning (3+ sessions in 30 minutes)
    if (recentSessions.last30min >= 3) {
      this.showAlert(
        '⚠️ You\'ve logged 3+ sessions in 30 minutes. Consider taking a longer break.',
        'warning'
      );
      return;
    }

    // High frequency warning (5+ sessions in 2 hours)
    if (recentSessions.last2hours >= 5) {
      this.showAlert(
        '💡 High frequency detected. Try some deep breathing or a short walk.',
        'info'
      );
      return;
    }

    // Batch logging warning (logging more than 3 at once)
    if (sessionCount > 3) {
      this.showAlert(
        'ℹ️ Logging multiple sessions at once? Try logging them as they happen for better tracking.',
        'info'
      );
    }
  }

  getRecentSessionPattern() {
    const now = new Date();
    const thirtyMinAgo = new Date(now.getTime() - (30 * 60 * 1000));
    const twoHoursAgo = new Date(now.getTime() - (2 * 60 * 60 * 1000));

    let last30min = 0;
    let last2hours = 0;

    this.userData.sessionTimes.forEach(timeStr => {
      const sessionTime = new Date(timeStr);
      if (sessionTime > thirtyMinAgo) last30min++;
      if (sessionTime > twoHoursAgo) last2hours++;
    });

    return { last30min, last2hours };
  }

  checkMotivationalMilestones() {
    const usagePercent = this.userData.dailyAllowance > 0
      ? (this.userData.sessionsLogged / this.userData.dailyAllowance) * 100
      : 0;

    // Milestone celebrations (only show once per milestone per day)
    const today = new Date().toISOString().split('T')[0];
    const milestoneKey = `milestones-${today}`;
    const todayMilestones = JSON.parse(localStorage.getItem(milestoneKey) || '{}');

    // 25% milestone
    if (usagePercent >= 25 && usagePercent < 50 && !todayMilestones['25']) {
      this.showAlert('🎯 25% of daily allowance used. You\'re pacing well!', 'success');
      todayMilestones['25'] = true;
    }

    // 50% milestone
    if (usagePercent >= 50 && usagePercent < 75 && !todayMilestones['50']) {
      this.showAlert('⚖️ Halfway through your daily allowance. How are you feeling?', 'info');
      todayMilestones['50'] = true;
    }

    // 75% milestone
    if (usagePercent >= 75 && usagePercent < 90 && !todayMilestones['75']) {
      this.showAlert('⚠️ 75% of allowance used. Consider slowing down for the rest of the day.', 'warning');
      todayMilestones['75'] = true;
    }

    // 90% milestone
    if (usagePercent >= 90 && usagePercent < 100 && !todayMilestones['90']) {
      this.showAlert('🚨 90% of allowance used. You\'re very close to your daily limit.', 'warning');
      todayMilestones['90'] = true;
    }

    // Save milestones
    localStorage.setItem(milestoneKey, JSON.stringify(todayMilestones));
  }

  getAlertType(status) {
    const alertTypes = {
      'great': 'success',
      'good': 'success',
      'caution': 'info',
      'warning': 'warning',
      'exceeded': 'error'
    };

    return alertTypes[status] || 'info';
  }

  // Undo last puff entry (for accidental logging)
  undoLastPuff() {
    if (this.userData.totalPuffsToday === 0) {
      return false;
    }

    // Remove last puff timestamp
    this.userData.puffTimes.pop();
    this.userData.totalPuffsToday--;

    // Update last puff time
    if (this.userData.puffTimes.length > 0) {
      this.userData.lastPuffTime = this.userData.puffTimes[this.userData.puffTimes.length - 1];
    } else {
      this.userData.lastPuffTime = null;
    }

    // For backward compatibility, also update session data
    if (this.userData.trackingMode === 'reduction' && this.userData.sessionsLogged > 0) {
      this.userData.sessionsLogged = Math.max(0, Math.ceil(this.userData.totalPuffsToday / this.userData.averagePuffsPerBreak));
      if (this.userData.sessionTimes.length > 0) {
        this.userData.lastSessionTime = this.userData.sessionTimes[this.userData.sessionTimes.length - 1];
      } else {
        this.userData.lastSessionTime = null;
      }
    }

    this.saveUserData();
    this.recordDailyStats();

    console.log(`↩️ Undid last puff. Total today: ${this.userData.totalPuffsToday}/${this.userData.dailyPuffTarget || 'unlimited'}`);
    this.showAlert('Last puff removed.', 'info');

    return true;
  }

  // Legacy function for backward compatibility
  undoLastSession() {
    return this.undoLastPuff();
  }

  checkFirstLaunch() {
    const hasLaunched = localStorage.getItem('byevape-launched');
    if (!hasLaunched) {
      this.isFirstLaunch = true;
      localStorage.setItem('byevape-launched', 'true');
    }
  }

  startApp() {
    console.log(`🚀 Starting app - isFirstLaunch: ${this.isFirstLaunch}, startDate: ${this.userData.startDate}`);

    if (this.isFirstLaunch || !this.userData.startDate) {
      console.log(`👋 First launch detected - showing onboarding`);
      this.showPage('onboarding');
    } else {
      console.log(`🔄 Returning user - showing tracker`);
      this.checkDailyReset();
      this.showPage('tracker');
      this.updateTrackerData();
      this.startCountdownTimer();
    }
  }

  // ===================================
  // NAVIGATION SYSTEM
  // ===================================

  initializeNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      item.addEventListener('click', (e) => {
        const page = item.getAttribute('data-page');
        this.navigateToPage(page);
      });
    });
  }

  navigateToPage(page) {
    if (this.isFirstLaunch && page !== 'onboarding') {
      return; // Prevent navigation during onboarding
    }
    
    this.currentPage = page;
    this.showPage(page);
    this.updateNavigation();
  }

  showPage(pageId) {
    console.log(`🔄 showPage called with pageId: ${pageId}`);

    // Hide all pages
    const pages = document.querySelectorAll('.page');
    console.log(`📄 Found ${pages.length} pages to hide`);
    pages.forEach((page, index) => {
      console.log(`   Hiding page ${index}: ${page.id}`);
      page.style.display = 'none';
    });

    // Show target page
    const targetPage = document.getElementById(`${pageId}-page`);
    console.log(`🎯 Target page element:`, targetPage);
    if (targetPage) {
      targetPage.style.display = 'block';
      console.log(`✅ Showing page: ${pageId}-page`);

      // Additional debugging
      const computedStyle = window.getComputedStyle(targetPage);
      console.log(`📊 Page styles - display: ${computedStyle.display}, visibility: ${computedStyle.visibility}, opacity: ${computedStyle.opacity}`);

      // Update page-specific data
      if (pageId === 'statistics') {
        this.updateStatisticsPage();
      } else if (pageId === 'settings') {
        this.updateSettingsData();
      }

    } else {
      console.error(`❌ Target page not found: ${pageId}-page`);
    }

    // Hide/show navigation based on page
    const bottomNav = document.getElementById('bottom-nav');
    console.log(`🧭 Bottom navigation element:`, bottomNav);
    if (pageId === 'onboarding') {
      if (bottomNav) {
        bottomNav.style.display = 'none';
        console.log(`🔒 Navigation hidden for onboarding`);
      }
    } else {
      if (bottomNav) {
        bottomNav.style.display = 'flex';
        console.log(`🔓 Navigation shown for ${pageId}`);
      }
    }

    console.log(`✨ showPage completed for: ${pageId}`);
  }

  updateNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      const page = item.getAttribute('data-page');
      if (page === this.currentPage) {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });
  }

  // ===================================
  // ONBOARDING FLOW
  // ===================================

  // New method for tracking mode selection
  selectTrackingMode(mode) {
    console.log(`🔄 Selecting tracking mode: ${mode}`);

    // Update button states with visual feedback
    const buttons = document.querySelectorAll('.tracking-mode-btn');
    buttons.forEach(btn => {
      btn.classList.remove('selected');
      if (btn.getAttribute('data-mode') === mode) {
        btn.classList.add('selected');
        console.log(`✅ Selected tracking mode: ${mode}`);
      }
    });

    this.userData.trackingMode = mode;

    // Set reduction timeline based on mode
    if (mode === 'reduction') {
      this.userData.reductionTimeline = 60; // Default to 60 days for reduction mode
    }

    // Enable continue button
    const continueBtn = document.getElementById('continue-step-1');
    if (continueBtn) {
      continueBtn.disabled = false;
      continueBtn.classList.remove('disabled');
      console.log('✅ Continue button enabled');
    }

    // Show mode description
    const modeDescriptions = {
      'reduction': 'You\'ll log each vaping session and gradually reduce over time.',
      'breaks': 'You\'ll take scheduled vaping breaks with timed intervals.'
    };

    this.showAlert(modeDescriptions[mode], 'info');
  }

  nextOnboardingStep() {
    console.log(`🔄 Moving from onboarding step ${this.currentOnboardingStep}`);

    // Handle different steps
    switch (this.currentOnboardingStep) {
      case 1:
        return this.handleStep1ToStep2();
      case 2:
        return this.handleStep2ToStep3();
      case 3:
        return this.handleStep3ToStep4();
      default:
        console.error('❌ Invalid onboarding step');
    }
  }

  handleStep1ToStep2() {
    // Validate tracking mode selection
    if (!this.userData.trackingMode) {
      this.showAlert('Please select a tracking method before continuing.', 'warning');
      return;
    }

    this.transitionToStep(2);
    this.showAlert(`Great! You've chosen ${this.userData.trackingMode === 'reduction' ? 'Session Tracking' : 'Break Timer'} mode.`, 'success');
  }

  handleStep2ToStep3() {
    // Validate daily vaping count
    const dailyCountInput = document.getElementById('daily-vaping-count');
    const dailyCount = dailyCountInput.value;

    if (!dailyCount || dailyCount < 1 || dailyCount > 100) {
      this.showAlert('Please enter a valid number between 1 and 100 daily vaping sessions.', 'warning');
      dailyCountInput.focus();
      return;
    }

    this.userData.dailyVapingCount = parseInt(dailyCount);
    this.userData.baselineVapeCount = parseInt(dailyCount); // Set baseline for reduction mode
    console.log(`✅ Daily vaping count set to: ${this.userData.dailyVapingCount}`);

    this.transitionToStep(3);
    this.showAlert(`Perfect! You vape ${dailyCount} times per day. Now let's configure your schedule.`, 'success');
  }

  handleStep3ToStep4() {
    // Validate sleep duration
    const sleepInput = document.getElementById('sleep-duration');
    const sleepDuration = sleepInput.value;

    if (!sleepDuration || sleepDuration < 4 || sleepDuration > 12) {
      this.showAlert('Please enter a valid sleep duration between 4 and 12 hours.', 'warning');
      sleepInput.focus();
      return;
    }

    this.userData.sleepDuration = parseInt(sleepDuration);
    this.userData.activeHours = 24 - this.userData.sleepDuration;
    console.log(`✅ Sleep duration set to: ${this.userData.sleepDuration}h (${this.userData.activeHours}h active)`);

    this.transitionToStep(4);
    this.showAlert(`Excellent! ${this.userData.activeHours} active hours per day. Now choose your quit speed.`, 'success');
  }

  transitionToStep(stepNumber) {
    // Hide current step
    const currentStep = document.getElementById(`onboarding-step-${this.currentOnboardingStep}`);
    if (currentStep) {
      currentStep.style.display = 'none';
    }

    // Show next step
    const nextStep = document.getElementById(`onboarding-step-${stepNumber}`);
    if (nextStep) {
      nextStep.style.display = 'block';
      this.currentOnboardingStep = stepNumber;
      console.log(`✅ Moved to onboarding step ${stepNumber}`);
    } else {
      console.error(`❌ Onboarding step ${stepNumber} not found`);
    }
  }

  selectCessationSpeed(speed) {
    console.log(`🔄 Selecting cessation speed: ${speed}`);

    // Update button states with visual feedback
    const buttons = document.querySelectorAll('.cessation-speed-btn');
    buttons.forEach(btn => {
      btn.classList.remove('selected');
      if (btn.getAttribute('data-speed') === speed) {
        btn.classList.add('selected');
        console.log(`✅ Selected button: ${speed}`);
      }
    });

    this.userData.cessationSpeed = speed;

    // Enable complete button with visual feedback
    const completeBtn = document.getElementById('complete-onboarding');
    if (completeBtn) {
      completeBtn.disabled = false;
      completeBtn.classList.remove('disabled');
      console.log('✅ Complete onboarding button enabled');
    }

    // Show speed description
    const speedDescriptions = {
      'fast': 'You\'ll quit in 30 days with a more aggressive reduction schedule.',
      'normal': 'You\'ll quit in 60 days with a balanced approach.',
      'relaxed': 'You\'ll quit in 90 days with a gentle, gradual reduction.'
    };

    this.showAlert(speedDescriptions[speed], 'info');
  }

  async completeOnboarding() {
    console.log('🔄 Completing onboarding...');

    if (!this.userData.cessationSpeed) {
      this.showAlert('Please select a cessation speed before continuing.', 'warning');
      return;
    }

    try {
      // Set start date and initial values
      this.userData.startDate = new Date().toISOString();
      this.userData.currentDay = 1;

      // Set reduction timeline based on cessation speed
      const timelineMap = { 'fast': 30, 'normal': 60, 'relaxed': 90 };
      this.userData.reductionTimeline = timelineMap[this.userData.cessationSpeed];

      // Initialize universal puff tracking
      this.userData.totalPuffsToday = 0;
      this.userData.puffTimes = [];
      this.userData.lastPuffTime = null;
      this.userData.lastRecordedDevicePuffValue = 0;

      // Initialize based on tracking mode
      if (this.userData.trackingMode === 'reduction') {
        // Initialize reduction tracking
        this.userData.dailyAllowance = this.calculateDailyAllowance(1);
        this.userData.sessionsLogged = 0;
        this.userData.sessionTimes = [];
        this.userData.lastSessionTime = null;

        console.log(`📉 Reduction mode initialized - Day 1 allowance: ${this.userData.dailyAllowance}`);
      } else {
        // Initialize break tracking (legacy mode)
        this.userData.totalBreaks = this.userData.dailyVapingCount;
        this.userData.breaksRemaining = this.userData.dailyVapingCount;
        this.userData.breaksTaken = 0;

        console.log(`⏰ Break mode initialized - ${this.userData.totalBreaks} breaks per day`);
      }

      // Initialize daily puff target
      this.updateDailyPuffTarget();

      // Save data
      this.saveUserData();
      this.isFirstLaunch = false;

      console.log('✅ Onboarding data saved:', this.userData);

      // Show completion message
      const modeText = this.userData.trackingMode === 'reduction' ? 'session tracking' : 'break timer';
      const timelineText = `${this.userData.reductionTimeline} days`;
      this.showAlert(`Welcome to ByeVape! Your ${modeText} plan (${timelineText}) is ready.`, 'success');

      // Small delay for better UX
      setTimeout(() => {
        // Navigate to tracker
        this.showPage('tracker');
        this.updateTrackerData();

        // Only start countdown timer for break mode
        if (this.userData.trackingMode === 'breaks') {
          this.startCountdownTimer();
        }

        console.log('✅ Onboarding completed - navigated to tracker');
      }, 1500);

    } catch (error) {
      console.error('❌ Error completing onboarding:', error);
      this.showAlert('There was an error setting up your account. Please try again.', 'error');
    }
  }

  // ===================================
  // TRACKER PAGE LOGIC
  // ===================================

  updateTrackerData() {
    console.log('🔄 Updating tracker data...');

    // Update day counter
    const startDate = new Date(this.userData.startDate);
    const today = new Date();
    const daysDiff = Math.floor((today - startDate) / (1000 * 60 * 60 * 24)) + 1;
    this.userData.currentDay = daysDiff;

    // Show appropriate tracking mode interface
    this.updateTrackerInterface();

    // Update based on tracking mode
    if (this.userData.trackingMode === 'reduction') {
      this.updateReductionTracker();
    } else {
      this.updateBreakTracker();
    }

    // Update settings page
    this.updateSettingsData();

    console.log(`✅ Tracker updated - Day ${this.userData.currentDay}`);
  }

  updateTrackerInterface() {
    const breakMode = document.getElementById('break-timer-mode');
    const reductionMode = document.getElementById('reduction-tracking-mode');

    if (this.userData.trackingMode === 'reduction') {
      if (breakMode) breakMode.style.display = 'none';
      if (reductionMode) reductionMode.style.display = 'block';
      console.log('📉 Showing reduction tracking interface');
    } else {
      if (breakMode) breakMode.style.display = 'block';
      if (reductionMode) reductionMode.style.display = 'none';
      console.log('⏰ Showing break timer interface');
    }

    // Update day counter and journey progress
    this.updateDayCounter();
  }

  updateDayCounter() {
    const currentDayDisplay = document.getElementById('current-day-display');
    const journeyProgress = document.getElementById('journey-progress');

    if (currentDayDisplay) {
      currentDayDisplay.textContent = this.userData.currentDay;
    }

    if (journeyProgress && this.userData.trackingMode === 'reduction') {
      const progressPercent = this.calculateReductionProgress();
      const remainingDays = this.userData.reductionTimeline - this.userData.currentDay;

      if (remainingDays > 0) {
        journeyProgress.textContent = `(${progressPercent.toFixed(0)}% complete, ${remainingDays} days left)`;
      } else {
        journeyProgress.textContent = '(Journey complete! 🎉)';
      }
    } else if (journeyProgress) {
      journeyProgress.textContent = 'of your journey';
    }
  }

  updateReductionTracker() {
    console.log('📉 Updating reduction tracker...');

    // Update daily puff target if needed
    if (this.userData.dailyPuffTarget === 0) {
      this.updateDailyPuffTarget();
    }

    // Update puff count display
    const puffsCount = document.getElementById('puffs-count');
    const dailyPuffTarget = document.getElementById('daily-puff-target');

    if (puffsCount) {
      puffsCount.textContent = this.userData.totalPuffsToday;
    }

    if (dailyPuffTarget) {
      dailyPuffTarget.textContent = this.userData.dailyPuffTarget;
    }

    // Update circular progress
    this.updateReductionProgress();

    // Update pace status
    this.updatePaceStatus();

    // Update last puff info
    this.updateLastPuffInfo();

    console.log(`📊 Puffs: ${this.userData.totalPuffsToday}/${this.userData.dailyPuffTarget}`);
  }

  updateBreakTracker() {
    console.log('⏰ Updating break tracker...');

    // Update UI elements safely
    const elements = {
      'current-day': this.userData.currentDay,
      'breaks-remaining': this.userData.breaksRemaining,
      'total-breaks': this.userData.totalBreaks,
      'breaks-taken': this.userData.breaksTaken
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element) {
        element.textContent = value;
      }
    });

    // Update progress bar
    const progressBar = document.getElementById('daily-progress');
    if (progressBar) {
      const progressPercent = this.userData.totalBreaks > 0
        ? ((this.userData.totalBreaks - this.userData.breaksRemaining) / this.userData.totalBreaks) * 100
        : 0;
      progressBar.style.width = `${progressPercent}%`;

      // Update progress bar color based on completion
      if (progressPercent < 50) {
        progressBar.style.backgroundColor = '#4CAF50'; // Green
      } else if (progressPercent < 80) {
        progressBar.style.backgroundColor = '#FF9800'; // Orange
      } else {
        progressBar.style.backgroundColor = '#F44336'; // Red
      }
    }

    // Update break button states based on remaining breaks
    if (this.userData.breaksRemaining <= 0) {
      this.updateBreakButtonStates('no-breaks');
    }
  }

  updateReductionProgress() {
    const progressRing = document.getElementById('reduction-progress-ring');
    if (!progressRing) return;

    // Calculate progress percentage based on puffs
    const progressPercent = this.userData.dailyPuffTarget > 0
      ? (this.userData.totalPuffsToday / this.userData.dailyPuffTarget) * 100
      : 0;

    // SVG circle calculations
    const radius = 90;
    const circumference = 2 * Math.PI * radius;
    const offset = circumference * (1 - (progressPercent / 100));

    // Update the progress ring
    progressRing.style.strokeDasharray = circumference;
    progressRing.style.strokeDashoffset = offset;

    // Update progress color based on status
    const paceCheck = this.checkPuffConsumptionPace();
    let strokeColor = '#4CAF50'; // Green - good

    if (paceCheck.status === 'exceeded') {
      strokeColor = '#F44336'; // Red - exceeded
    } else if (paceCheck.status === 'warning') {
      strokeColor = '#FF9800'; // Orange - warning
    } else if (paceCheck.status === 'caution') {
      strokeColor = '#FFC107'; // Yellow - caution
    }

    progressRing.style.stroke = strokeColor;
    progressRing.style.transition = 'stroke-dashoffset 0.5s ease-in-out, stroke 0.3s ease-in-out';

    console.log(`🔄 Progress updated: ${progressPercent.toFixed(1)}% (${this.userData.totalPuffsToday}/${this.userData.dailyPuffTarget})`);
  }

  updatePaceStatus() {
    const paceStatus = document.getElementById('pace-status');
    if (!paceStatus) return;

    const paceCheck = this.checkPuffConsumptionPace();

    const statusTexts = {
      'great': 'Excellent!',
      'good': 'On track',
      'caution': 'Slightly ahead',
      'warning': 'Ahead of pace',
      'exceeded': 'Limit reached'
    };

    paceStatus.textContent = statusTexts[paceCheck.status] || 'On track';

    // Update status message if needed
    if (paceCheck.message) {
      this.showStatusMessage(paceCheck.message, paceCheck.status);
    }
  }

  updateLastPuffInfo() {
    const lastPuffInfo = document.getElementById('last-puff-info');
    const lastPuffTime = document.getElementById('last-puff-time');

    if (!lastPuffInfo || !lastPuffTime) return;

    if (this.userData.lastPuffTime) {
      const lastTime = new Date(this.userData.lastPuffTime);
      const now = new Date();
      const diffMinutes = Math.floor((now - lastTime) / (1000 * 60));

      let timeText;
      if (diffMinutes < 1) {
        timeText = 'just now';
      } else if (diffMinutes < 60) {
        timeText = `${diffMinutes}m ago`;
      } else {
        const diffHours = Math.floor(diffMinutes / 60);
        timeText = `${diffHours}h ${diffMinutes % 60}m ago`;
      }

      lastPuffTime.textContent = timeText;
      lastPuffInfo.style.display = 'block';
    } else {
      lastPuffInfo.style.display = 'none';
    }
  }

  // Legacy function for backward compatibility
  updateLastSessionInfo() {
    return this.updateLastPuffInfo();
  }

  showStatusMessage(message, status = 'info') {
    const statusMessage = document.getElementById('status-message');
    const statusIcon = document.getElementById('status-icon');
    const statusText = document.getElementById('status-text');

    if (!statusMessage || !statusIcon || !statusText) return;

    // Set icon based on status
    const icons = {
      'great': '🎉',
      'good': '✅',
      'caution': '💡',
      'warning': '⚠️',
      'exceeded': '🚨',
      'info': 'ℹ️'
    };

    statusIcon.textContent = icons[status] || icons.info;
    statusText.textContent = message;

    // Remove existing status classes
    statusMessage.classList.remove('success', 'warning', 'error', 'info');

    // Add appropriate status class
    const statusClasses = {
      'great': 'success',
      'good': 'success',
      'caution': 'info',
      'warning': 'warning',
      'exceeded': 'error'
    };

    statusMessage.classList.add(statusClasses[status] || 'info');
    statusMessage.style.display = 'block';

    // Auto-hide after 5 seconds for non-critical messages
    if (status !== 'exceeded' && status !== 'warning') {
      setTimeout(() => {
        if (statusMessage) {
          statusMessage.style.display = 'none';
        }
      }, 5000);
    }
  }

  takeVapingBreak() {
    console.log('🔄 Taking vaping break...');

    if (this.userData.breaksRemaining <= 0) {
      this.showAlert('🎉 No breaks remaining for today. You\'re doing amazing!', 'success');
      this.updateBreakButtonStates('no-breaks');
      return;
    }

    // Record the break
    this.userData.breaksRemaining--;
    this.userData.breaksTaken++;

    // Save current time as last break time
    const now = new Date();
    localStorage.setItem('byevape-last-break-time', now.toISOString());

    // Save user data and record daily stats
    this.saveUserData();
    this.recordDailyStats();
    this.updateTrackerData();

    // Calculate next break time
    const hoursPerDay = 18;
    const minutesBetweenBreaks = (hoursPerDay * 60) / this.userData.totalBreaks;
    this.nextBreakTime = new Date(now.getTime() + (minutesBetweenBreaks * 60 * 1000));
    localStorage.setItem('byevape-next-break-time', this.nextBreakTime.toISOString());

    // Restart timer
    this.startCountdownTimer();

    // Show motivational message based on progress
    const progressPercent = (this.userData.breaksTaken / this.userData.totalBreaks) * 100;
    let message = '✅ Break taken! ';

    if (progressPercent <= 25) {
      message += 'Great start! You\'re building a healthy routine.';
    } else if (progressPercent <= 50) {
      message += 'You\'re doing amazing! Keep up the momentum.';
    } else if (progressPercent <= 75) {
      message += 'Excellent progress! Your body is thanking you.';
    } else {
      message += 'Outstanding! You\'re almost done for today.';
    }

    this.showAlert(message, 'success');

    console.log(`✅ Break taken - ${this.userData.breaksRemaining} remaining, next break at ${this.formatTime(this.nextBreakTime)}`);
  }

  takeEarlyBreak() {
    console.log('🔄 Taking early vaping break...');

    if (this.userData.breaksRemaining <= 0) {
      this.showAlert('🎉 No breaks remaining for today. You\'re doing amazing!', 'success');
      this.updateBreakButtonStates('no-breaks');
      return;
    }

    // Calculate penalty time (add 25% extra time to next break)
    const hoursPerDay = 18;
    const minutesBetweenBreaks = (hoursPerDay * 60) / this.userData.totalBreaks;
    const penaltyMinutes = minutesBetweenBreaks * 0.25; // 25% penalty

    // Record the break
    this.userData.breaksRemaining--;
    this.userData.breaksTaken++;

    // Save current time as last break time
    const now = new Date();
    localStorage.setItem('byevape-last-break-time', now.toISOString());

    // Save user data and record daily stats
    this.saveUserData();
    this.recordDailyStats();
    this.updateTrackerData();

    // Calculate next break time with penalty
    const totalWaitTime = (minutesBetweenBreaks + penaltyMinutes) * 60 * 1000;
    this.nextBreakTime = new Date(now.getTime() + totalWaitTime);
    localStorage.setItem('byevape-next-break-time', this.nextBreakTime.toISOString());

    // Restart timer
    this.startCountdownTimer();

    // Show warning about early break with specific penalty time
    const penaltyText = penaltyMinutes < 60
      ? `${Math.round(penaltyMinutes)} minutes`
      : `${Math.round(penaltyMinutes / 60 * 10) / 10} hours`;

    this.showAlert(
      `⚠️ Early break taken. ${penaltyText} added to your next break interval as a gentle reminder to stick to your schedule.`,
      'warning'
    );

    console.log(`⚠️ Early break taken - penalty: ${penaltyMinutes.toFixed(1)} minutes, next break at ${this.formatTime(this.nextBreakTime)}`);
  }

  // ===================================
  // SETTINGS PAGE LOGIC
  // ===================================

  updateSettingsData() {
    // Update tracking mode display
    const trackingModeText = this.userData.trackingMode === 'reduction' ? 'Session Tracking' : 'Break Timer';
    this.updateElement('settings-tracking-mode', trackingModeText);

    // Show/hide appropriate settings rows
    this.updateSettingsInterface();

    // Update common settings
    const speedText = {
      'fast': 'Fast (30 days)',
      'normal': 'Normal (60 days)',
      'relaxed': 'Relaxed (90 days)'
    };
    this.updateElement('settings-cessation-speed', speedText[this.userData.cessationSpeed]);
    this.updateElement('settings-days-completed', this.userData.currentDay);

    // Update mode-specific settings
    if (this.userData.trackingMode === 'reduction') {
      this.updateElement('settings-daily-allowance', this.userData.dailyAllowance);
      this.updateElement('settings-baseline-count', this.userData.baselineVapeCount);
      this.updateElement('settings-sleep-duration', `${this.userData.sleepDuration} hours`);
    } else {
      this.updateElement('settings-daily-breaks', this.userData.totalBreaks);
    }
  }

  updateSettingsInterface() {
    const dailyBreaksRow = document.getElementById('daily-breaks-row');
    const dailyAllowanceRow = document.getElementById('daily-allowance-row');
    const baselineCountRow = document.getElementById('baseline-count-row');
    const sleepDurationRow = document.getElementById('sleep-duration-row');
    const breakModeSettings = document.getElementById('break-mode-settings');
    const reductionModeSettings = document.getElementById('reduction-mode-settings');

    if (this.userData.trackingMode === 'reduction') {
      // Show reduction mode elements
      if (dailyBreaksRow) dailyBreaksRow.style.display = 'none';
      if (dailyAllowanceRow) dailyAllowanceRow.style.display = 'flex';
      if (baselineCountRow) baselineCountRow.style.display = 'flex';
      if (sleepDurationRow) sleepDurationRow.style.display = 'flex';
      if (breakModeSettings) breakModeSettings.style.display = 'none';
      if (reductionModeSettings) reductionModeSettings.style.display = 'block';
    } else {
      // Show break mode elements
      if (dailyBreaksRow) dailyBreaksRow.style.display = 'flex';
      if (dailyAllowanceRow) dailyAllowanceRow.style.display = 'none';
      if (baselineCountRow) baselineCountRow.style.display = 'none';
      if (sleepDurationRow) sleepDurationRow.style.display = 'none';
      if (breakModeSettings) breakModeSettings.style.display = 'block';
      if (reductionModeSettings) reductionModeSettings.style.display = 'none';
    }
  }

  saveSettings() {
    let settingsChanged = false;

    // Handle break mode settings
    if (this.userData.trackingMode === 'breaks') {
      const newDailyBreaks = document.getElementById('modify-daily-breaks').value;

      if (newDailyBreaks && newDailyBreaks > 0) {
        this.userData.totalBreaks = parseInt(newDailyBreaks);
        this.userData.breaksRemaining = parseInt(newDailyBreaks);
        settingsChanged = true;
      }
    }

    // Handle reduction mode settings
    if (this.userData.trackingMode === 'reduction') {
      const newSleepDuration = document.getElementById('modify-sleep-duration').value;
      const newWarningSensitivity = document.getElementById('modify-warning-sensitivity').value;

      if (newSleepDuration && newSleepDuration >= 4 && newSleepDuration <= 12) {
        this.userData.sleepDuration = parseInt(newSleepDuration);
        this.userData.activeHours = 24 - this.userData.sleepDuration;
        settingsChanged = true;
      }

      if (newWarningSensitivity) {
        this.userData.warningSensitivity = newWarningSensitivity;
        // Adjust warning threshold based on sensitivity
        const thresholds = { 'low': 0.9, 'normal': 0.8, 'high': 0.7 };
        this.userData.warningThreshold = thresholds[newWarningSensitivity] || 0.8;
        settingsChanged = true;
      }
    }

    // Handle common settings
    const newCessationSpeed = document.getElementById('modify-cessation-speed').value;

    if (newCessationSpeed) {
      this.userData.cessationSpeed = newCessationSpeed;

      // Update reduction timeline if in reduction mode
      if (this.userData.trackingMode === 'reduction') {
        const timelineMap = { 'fast': 30, 'normal': 60, 'relaxed': 90 };
        this.userData.reductionTimeline = timelineMap[newCessationSpeed];

        // Recalculate daily allowance with new timeline
        this.userData.dailyAllowance = this.calculateDailyAllowance();
      }

      settingsChanged = true;
    }

    if (settingsChanged) {
      this.saveUserData();
      this.updateTrackerData();
      this.updateSettingsData();
      this.showAlert('Settings saved successfully!', 'success');

      // Clear form inputs
      this.clearSettingsForm();
    } else {
      this.showAlert('No changes to save.', 'info');
    }
  }

  clearSettingsForm() {
    const inputs = [
      'modify-daily-breaks',
      'modify-sleep-duration',
      'modify-warning-sensitivity',
      'modify-cessation-speed'
    ];

    inputs.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.value = '';
      }
    });
  }

  resetApp() {
    if (confirm('Are you sure you want to reset the app? This will delete all your progress.')) {
      localStorage.clear();
      location.reload();
    }
  }

  exportData() {
    const dataStr = JSON.stringify(this.userData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'byevape-data.json';
    link.click();
    
    URL.revokeObjectURL(url);
    this.showAlert('Data exported successfully!', 'success');
  }

  // ===================================
  // TIMER FUNCTIONALITY
  // ===================================

  startCountdownTimer() {
    console.log('🔄 Starting countdown timer...');

    // Calculate realistic time between breaks (18 hours / number of breaks)
    const hoursPerDay = 18; // Excluding 6 hours sleep (6:00 AM - 12:00 AM)
    const minutesBetweenBreaks = (hoursPerDay * 60) / this.userData.totalBreaks;

    console.log(`⏰ Minutes between breaks: ${minutesBetweenBreaks.toFixed(1)} (${this.userData.totalBreaks} breaks per day)`);

    // Check if we have a saved next break time
    const savedNextBreakTime = localStorage.getItem('byevape-next-break-time');
    const now = new Date();

    if (savedNextBreakTime) {
      const savedTime = new Date(savedNextBreakTime);
      if (savedTime > now) {
        // Use saved time if it's in the future
        this.nextBreakTime = savedTime;
        console.log(`✅ Restored next break time: ${this.formatTime(this.nextBreakTime)}`);
      } else {
        // Saved time is in the past, calculate new time
        this.calculateNextBreakTime(minutesBetweenBreaks);
      }
    } else {
      // No saved time, calculate new time
      this.calculateNextBreakTime(minutesBetweenBreaks);
    }

    this.updateCountdownDisplay();

    // Clear any existing interval
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }

    // Update every second
    this.countdownInterval = setInterval(() => {
      this.updateCountdownDisplay();
    }, 1000);

    console.log(`✅ Timer started - next break at ${this.formatTime(this.nextBreakTime)}`);
  }

  calculateNextBreakTime(minutesBetweenBreaks) {
    const now = new Date();
    const lastBreakTime = localStorage.getItem('byevape-last-break-time');

    if (lastBreakTime) {
      // Calculate based on last break time
      const lastBreak = new Date(lastBreakTime);
      this.nextBreakTime = new Date(lastBreak.getTime() + (minutesBetweenBreaks * 60 * 1000));
      console.log(`📅 Calculated from last break: ${this.formatTime(lastBreak)} + ${minutesBetweenBreaks.toFixed(1)}min`);
    } else {
      // First break of the day - start immediately or after interval
      if (this.userData.breaksTaken === 0) {
        // First break available immediately
        this.nextBreakTime = new Date(now.getTime() + (60 * 1000)); // 1 minute from now
        console.log(`🎯 First break available in 1 minute`);
      } else {
        // Calculate from current time
        this.nextBreakTime = new Date(now.getTime() + (minutesBetweenBreaks * 60 * 1000));
        console.log(`⏰ Next break in ${minutesBetweenBreaks.toFixed(1)} minutes`);
      }
    }

    // Save the calculated time
    localStorage.setItem('byevape-next-break-time', this.nextBreakTime.toISOString());
  }

  updateCountdownDisplay() {
    const now = new Date();
    const timeLeft = this.nextBreakTime - now;
    const timerElement = document.getElementById('countdown-timer');

    if (!timerElement) return;

    if (timeLeft <= 0) {
      // Time for next break
      timerElement.textContent = '00:00:00';
      this.showAlert('🎯 Time for your next vaping break!', 'success');
      this.updateBreakButtonStates('available');
      clearInterval(this.countdownInterval);
      return;
    }

    // Calculate time components
    const totalHours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

    // Adaptive display format
    let timeString;
    if (totalHours > 0) {
      // Show hours:minutes:seconds for long waits
      timeString = `${totalHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      // Show minutes:seconds for waits under 1 hour
      timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    timerElement.textContent = timeString;

    // Update break button states
    this.updateBreakButtonStates('waiting');

    // Update circular progress
    this.updateCircularProgress(timeLeft);

    // Update subtitle with motivational message
    this.updateTimerSubtitle(timeLeft);
  }

  updateTimerSubtitle(timeLeft) {
    const subtitleElement = document.querySelector('#tracker-page .text-center p');
    if (!subtitleElement) return;

    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 4) {
      subtitleElement.textContent = `Next break at ${this.formatTime(this.nextBreakTime)}`;
    } else if (hours > 1) {
      subtitleElement.textContent = `${hours}h ${minutes}m until next break`;
    } else if (minutes > 30) {
      subtitleElement.textContent = `${minutes} minutes until next break`;
    } else if (minutes > 5) {
      subtitleElement.textContent = `${minutes} minutes - stay strong!`;
    } else {
      subtitleElement.textContent = `Almost there - ${minutes}m ${Math.floor((timeLeft % (1000 * 60)) / 1000)}s`;
    }
  }

  updateBreakButtonStates(state) {
    const takeBreakBtn = document.getElementById('take-break-btn');
    const earlyBreakBtn = document.getElementById('early-break-btn');

    if (!takeBreakBtn || !earlyBreakBtn) return;

    switch (state) {
      case 'available':
        takeBreakBtn.disabled = false;
        takeBreakBtn.textContent = '🎯 Take Break';
        takeBreakBtn.className = 'btn btn-success btn-lg';
        earlyBreakBtn.style.display = 'none';
        break;

      case 'waiting':
        takeBreakBtn.disabled = true;
        takeBreakBtn.textContent = '⏰ Break Not Ready';
        takeBreakBtn.className = 'btn btn-secondary btn-lg';
        earlyBreakBtn.style.display = this.userData.breaksRemaining > 0 ? 'inline-block' : 'none';
        break;

      case 'no-breaks':
        takeBreakBtn.disabled = true;
        takeBreakBtn.textContent = '✅ All Breaks Used Today';
        takeBreakBtn.className = 'btn btn-outline-secondary btn-lg';
        earlyBreakBtn.style.display = 'none';
        break;
    }
  }

  updateCircularProgress(timeLeft) {
    const progressRing = document.querySelector('.progress-ring-fill');
    if (!progressRing) return;

    // Calculate total time between breaks
    const hoursPerDay = 18;
    const minutesBetweenBreaks = (hoursPerDay * 60) / this.userData.totalBreaks;
    const totalTime = minutesBetweenBreaks * 60 * 1000; // in milliseconds

    // Calculate progress (how much time has passed since last break)
    const timeElapsed = totalTime - timeLeft;
    const progress = Math.max(0, Math.min(1, timeElapsed / totalTime));

    // SVG circle calculations
    const radius = 90; // radius of the progress circle
    const circumference = 2 * Math.PI * radius;
    const offset = circumference * (1 - progress);

    // Update the progress ring
    progressRing.style.strokeDasharray = circumference;
    progressRing.style.strokeDashoffset = offset;

    // Update progress color based on completion
    if (progress < 0.5) {
      progressRing.style.stroke = '#4CAF50'; // Green - early stage
    } else if (progress < 0.8) {
      progressRing.style.stroke = '#FF9800'; // Orange - getting close
    } else {
      progressRing.style.stroke = '#F44336'; // Red - almost time
    }

    // Add subtle animation for smooth transitions
    progressRing.style.transition = 'stroke-dashoffset 0.5s ease-in-out, stroke 0.3s ease-in-out';
  }

  // ===================================
  // STATISTICS CALCULATIONS
  // ===================================

  calculateStatistics() {
    const today = new Date();
    const sevenDaysAgo = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));

    // Get last 7 days of data
    const weeklyData = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today.getTime() - (i * 24 * 60 * 60 * 1000));
      const dateStr = date.toISOString().split('T')[0];
      const dayData = this.dailyHistory[dateStr];

      if (dayData) {
        weeklyData.push(dayData);
      }
    }

    // Calculate weekly average
    const weeklyAverage = weeklyData.length > 0
      ? (weeklyData.reduce((sum, day) => sum + day.breaksTaken, 0) / weeklyData.length).toFixed(1)
      : 0;

    // Find best day (lowest breaks taken)
    let bestDay = { day: 'N/A', breaks: 0 };
    if (weeklyData.length > 0) {
      const best = weeklyData.reduce((min, day) =>
        day.breaksTaken < min.breaksTaken ? day : min
      );
      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const dayName = dayNames[new Date(best.date).getDay()];
      bestDay = { day: dayName, breaks: best.breaksTaken };
    }

    // Calculate improvement (compare this week to last week)
    let improvement = 0;
    if (weeklyData.length >= 7) {
      const thisWeekAvg = weeklyData.slice(-7).reduce((sum, day) => sum + day.breaksTaken, 0) / 7;
      const lastWeekData = [];

      for (let i = 13; i >= 7; i--) {
        const date = new Date(today.getTime() - (i * 24 * 60 * 60 * 1000));
        const dateStr = date.toISOString().split('T')[0];
        const dayData = this.dailyHistory[dateStr];
        if (dayData) lastWeekData.push(dayData);
      }

      if (lastWeekData.length > 0) {
        const lastWeekAvg = lastWeekData.reduce((sum, day) => sum + day.breaksTaken, 0) / lastWeekData.length;
        improvement = ((thisWeekAvg - lastWeekAvg) / lastWeekAvg * 100).toFixed(1);
      }
    }

    // Calculate current streak (days meeting or beating target)
    let streak = 0;
    for (let i = 0; i < 30; i++) {
      const date = new Date(today.getTime() - (i * 24 * 60 * 60 * 1000));
      const dateStr = date.toISOString().split('T')[0];
      const dayData = this.dailyHistory[dateStr];

      if (dayData && dayData.breaksTaken <= dayData.totalBreaks) {
        streak++;
      } else {
        break;
      }
    }

    return {
      weeklyAverage: parseFloat(weeklyAverage),
      bestDay,
      improvement: parseFloat(improvement),
      streak,
      totalDays: Object.keys(this.dailyHistory).length,
      weeklyData
    };
  }

  calculateWeeklySessionsAverage() {
    const today = new Date();
    const weeklyData = [];

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today.getTime() - (i * 24 * 60 * 60 * 1000));
      const dateStr = date.toISOString().split('T')[0];
      const dayData = this.dailyHistory[dateStr];

      if (dayData && dayData.trackingMode === 'reduction') {
        weeklyData.push(dayData.sessionsLogged || 0);
      }
    }

    return weeklyData.length > 0
      ? weeklyData.reduce((sum, sessions) => sum + sessions, 0) / weeklyData.length
      : 0;
  }

  findBestAdherenceDay() {
    const today = new Date();
    let bestDay = { day: 'No data yet', adherence: -1 };

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today.getTime() - (i * 24 * 60 * 60 * 1000));
      const dateStr = date.toISOString().split('T')[0];
      const dayData = this.dailyHistory[dateStr];

      if (dayData && dayData.trackingMode === 'reduction' && dayData.dailyAllowance > 0) {
        const adherence = 1 - (dayData.sessionsLogged / dayData.dailyAllowance);

        if (adherence > bestDay.adherence) {
          const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
          const dayName = dayNames[date.getDay()];
          bestDay = {
            day: `${dayName} (${dayData.sessionsLogged}/${dayData.dailyAllowance})`,
            adherence
          };
        }
      }
    }

    return bestDay.day;
  }

  updateStatisticsPage() {
    console.log('🔄 Updating statistics page...');

    // Record today's stats first
    this.recordDailyStats();

    // Show appropriate statistics based on tracking mode
    this.updateStatisticsInterface();

    const stats = this.calculateStatistics();
    console.log('📊 Calculated stats:', stats);

    if (this.userData.trackingMode === 'reduction') {
      this.updateReductionStatistics(stats);
    } else {
      this.updateBreakStatistics(stats);
    }

    // Update daily breakdown (common for both modes)
    this.updateDailyBreakdown(stats.weeklyData);

    console.log('✅ Statistics page updated');
  }

  updateStatisticsInterface() {
    const breakModeStats = document.getElementById('break-mode-stats');
    const reductionModeStats = document.getElementById('reduction-mode-stats');

    if (this.userData.trackingMode === 'reduction') {
      if (breakModeStats) breakModeStats.style.display = 'none';
      if (reductionModeStats) reductionModeStats.style.display = 'block';
    } else {
      if (breakModeStats) breakModeStats.style.display = 'block';
      if (reductionModeStats) reductionModeStats.style.display = 'none';
    }
  }

  updateReductionStatistics(stats) {
    // Calculate reduction-specific statistics
    const reductionProgress = this.calculateReductionProgress();
    const weeklySessionsAverage = this.calculateWeeklySessionsAverage();
    const bestAdherenceDay = this.findBestAdherenceDay();

    this.updateElement('weekly-sessions-average', weeklySessionsAverage.toFixed(1));
    this.updateElement('reduction-progress-text', `${reductionProgress.toFixed(0)}% complete`);
    this.updateElement('allowance-trend', 'Decreasing gradually');
    this.updateElement('best-adherence-day', bestAdherenceDay);
  }

  updateBreakStatistics(stats) {
    // Update weekly overview
    this.updateElement('weekly-average', stats.weeklyAverage);
    this.updateElement('best-day-text', `${stats.bestDay.breaks} breaks (${stats.bestDay.day})`);

    // Update improvement with proper formatting
    const improvementElement = document.querySelector('#statistics-page .improvement-text');
    if (improvementElement) {
      const sign = stats.improvement > 0 ? '+' : '';
      improvementElement.textContent = `${sign}${stats.improvement}% from last week`;
      improvementElement.className = stats.improvement <= 0 ? 'text-success' : 'text-warning';
    }

    // Update achievements
    this.updateAchievements(stats);

    // Update daily breakdown
    this.updateDailyBreakdown(stats.weeklyData);
  }

  updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = value;
    }
  }

  updateAchievements(stats) {
    const achievementsContainer = document.querySelector('#statistics-page .achievements-container');
    if (!achievementsContainer) return;

    let achievements = [];

    // Streak achievement
    if (stats.streak >= 3) {
      achievements.push({
        type: 'success',
        icon: '🎉',
        title: `${stats.streak} Day Streak!`,
        description: `You've stayed within your limit for ${stats.streak} days.`
      });
    }

    // Progress milestone
    const totalReduction = this.userData.dailyVapingCount - this.userData.totalBreaks;
    const reductionPercent = ((totalReduction / this.userData.dailyVapingCount) * 100).toFixed(0);
    if (reductionPercent >= 25) {
      achievements.push({
        type: 'info',
        icon: '📈',
        title: 'Progress Milestone!',
        description: `${reductionPercent}% reduction from your starting point.`
      });
    }

    // Weekly improvement
    if (stats.improvement < -10) {
      achievements.push({
        type: 'success',
        icon: '⭐',
        title: 'Weekly Improvement!',
        description: `${Math.abs(stats.improvement)}% improvement this week.`
      });
    }

    // Update achievements HTML
    achievementsContainer.innerHTML = achievements.map(achievement => `
      <div class="alert alert-${achievement.type}">
        ${achievement.icon} <strong>${achievement.title}</strong> ${achievement.description}
      </div>
    `).join('');
  }

  updateDailyBreakdown(weeklyData) {
    const breakdownContainer = document.querySelector('#statistics-page .daily-breakdown');
    if (!breakdownContainer) return;

    const today = new Date();
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    let breakdownHTML = '';

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today.getTime() - (i * 24 * 60 * 60 * 1000));
      const dateStr = date.toISOString().split('T')[0];
      const dayData = this.dailyHistory[dateStr];

      let dayLabel = i === 0 ? 'Today' : i === 1 ? 'Yesterday' : `${i} days ago`;
      let progressPercent = 0;
      let breakText = 'No data';
      let progressClass = '';

      if (dayData) {
        progressPercent = dayData.totalBreaks > 0
          ? (dayData.breaksTaken / dayData.totalBreaks * 100)
          : 0;
        breakText = `${dayData.breaksTaken}/${dayData.totalBreaks}`;

        if (progressPercent <= 75) progressClass = 'success';
        else if (progressPercent <= 100) progressClass = '';
        else progressClass = 'warning';
      }

      breakdownHTML += `
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>${dayLabel}</span>
          <div class="d-flex align-items-center gap-2">
            <div class="progress" style="width: 100px;">
              <div class="progress-bar ${progressClass}" style="width: ${Math.min(progressPercent, 100)}%;"></div>
            </div>
            <span>${breakText}</span>
          </div>
        </div>
      `;
    }

    breakdownContainer.innerHTML = breakdownHTML;
  }

  // ===================================
  // UTILITY FUNCTIONS
  // ===================================

  showAlert(message, type = 'info') {
    try {
      // Create alert element
      const alert = document.createElement('div');
      alert.className = `alert alert-${type}`;
      alert.innerHTML = `<strong>${type.charAt(0).toUpperCase() + type.slice(1)}!</strong> ${message}`;

      // Insert at top of current page
      const currentPage = document.getElementById(`${this.currentPage}-page`);
      if (currentPage) {
        const container = currentPage.querySelector('.container');
        if (container) {
          container.insertBefore(alert, container.firstChild);

          // Remove after 3 seconds
          setTimeout(() => {
            if (alert.parentNode) {
              alert.parentNode.removeChild(alert);
            }
          }, 3000);
        } else {
          console.warn('Container not found for alert:', message);
        }
      } else {
        console.warn('Current page not found for alert:', message);
        // Fallback: log to console
        console.log(`${type.toUpperCase()}: ${message}`);
      }
    } catch (error) {
      console.error('Error showing alert:', error);
      console.log(`${type.toUpperCase()}: ${message}`);
    }
  }

  formatTime(date) {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatDate(date) {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  // Daily reset functionality
  checkDailyReset() {
    console.log('🔄 Checking for daily reset...');

    const lastResetDate = localStorage.getItem('byevape-last-reset');
    const now = new Date();
    const today = now.toDateString();

    // Handle timezone changes and edge cases
    const todayISO = now.toISOString().split('T')[0];
    const lastResetISO = localStorage.getItem('byevape-last-reset-iso');

    // Also check if we've crossed midnight since last app use
    const startDate = new Date(this.userData.startDate);
    const daysSinceStart = Math.floor((now - startDate) / (1000 * 60 * 60 * 24)) + 1;

    console.log(`📅 Last reset: ${lastResetDate}, Today: ${today}, Days since start: ${daysSinceStart}`);
    console.log(`📅 ISO dates - Last: ${lastResetISO}, Today: ${todayISO}`);

    // Check for data corruption or invalid dates
    if (!this.validateDateIntegrity(daysSinceStart)) {
      console.warn('⚠️ Date integrity issue detected, performing corrective reset');
      this.performCorrectiveReset();
      return;
    }

    if (lastResetDate !== today || lastResetISO !== todayISO || daysSinceStart !== this.userData.currentDay) {
      console.log('🔄 Performing daily reset...');

      // Update current day
      this.userData.currentDay = daysSinceStart;

      // Reset universal puff tracking data
      this.userData.totalPuffsToday = 0;
      this.userData.puffTimes = [];
      this.userData.lastPuffTime = null;

      // Reset daily counters based on tracking mode
      if (this.userData.trackingMode === 'reduction') {
        // Reset reduction tracking data
        this.userData.sessionsLogged = 0;
        this.userData.sessionTimes = [];
        this.userData.lastSessionTime = null;

        // Calculate new daily allowance
        this.userData.dailyAllowance = this.calculateDailyAllowance(daysSinceStart);

        console.log(`📉 Reduction mode reset - Day ${daysSinceStart}, allowance: ${this.userData.dailyAllowance}`);
      } else {
        // Reset break tracking data (legacy mode)
        const oldBreaksRemaining = this.userData.breaksRemaining;
        this.userData.breaksRemaining = this.userData.totalBreaks;
        this.userData.breaksTaken = 0;

        // Clear timer-related localStorage
        localStorage.removeItem('byevape-last-break-time');
        localStorage.removeItem('byevape-next-break-time');

        // Apply progressive reduction based on cessation speed
        this.applyProgressiveReduction();

        console.log(`🔄 Break mode reset - Day ${daysSinceStart}, breaks: ${this.userData.totalBreaks}`);
      }

      // Update daily puff target for the new day
      this.updateDailyPuffTarget();

      // Save reset date and user data with enhanced tracking
      localStorage.setItem('byevape-last-reset', today);
      localStorage.setItem('byevape-last-reset-iso', now.toISOString().split('T')[0]);
      localStorage.setItem('byevape-last-reset-timestamp', now.toISOString());
      this.saveUserData();

      // Show welcome message for new day
      if (daysSinceStart > 1) {
        let message;
        if (this.userData.trackingMode === 'reduction') {
          const reductionPercent = this.calculateReductionProgress();
          message = daysSinceStart <= 7
            ? `🌅 Day ${daysSinceStart} - Welcome back! You can have ${this.userData.dailyAllowance} sessions today.`
            : `🌅 Day ${daysSinceStart} - Amazing progress! ${reductionPercent.toFixed(0)}% through your journey. ${this.userData.dailyAllowance} sessions allowed today.`;
        } else {
          message = daysSinceStart <= 7
            ? `🌅 Day ${daysSinceStart} - Welcome back! You have ${this.userData.totalBreaks} breaks today.`
            : `🌅 Day ${daysSinceStart} - You're doing amazing! ${this.userData.totalBreaks} breaks available today.`;
        }
        this.showAlert(message, 'info');
      }

      const allowanceText = this.userData.trackingMode === 'reduction'
        ? `${this.userData.dailyAllowance} sessions allowed`
        : `${this.userData.totalBreaks} breaks available`;
      console.log(`✅ Daily reset complete - Day ${daysSinceStart}, ${allowanceText}`);
    } else {
      console.log('ℹ️ No daily reset needed');
    }
  }

  validateDateIntegrity(calculatedDaysSinceStart) {
    // Check for impossible values
    if (calculatedDaysSinceStart < 1 || calculatedDaysSinceStart > 365) {
      console.error(`❌ Invalid days since start: ${calculatedDaysSinceStart}`);
      return false;
    }

    // Check for time travel (current day going backwards)
    if (calculatedDaysSinceStart < this.userData.currentDay - 1) {
      console.error(`❌ Time travel detected: ${calculatedDaysSinceStart} < ${this.userData.currentDay}`);
      return false;
    }

    // Check for massive jumps (more than 7 days)
    if (calculatedDaysSinceStart > this.userData.currentDay + 7) {
      console.warn(`⚠️ Large time jump detected: ${calculatedDaysSinceStart} vs ${this.userData.currentDay}`);
      // This is a warning, not an error - user might have been away
    }

    // Check start date validity
    const startDate = new Date(this.userData.startDate);
    const now = new Date();
    if (startDate > now) {
      console.error('❌ Start date is in the future');
      return false;
    }

    return true;
  }

  performCorrectiveReset() {
    console.log('🔧 Performing corrective reset...');

    const now = new Date();

    // If start date is invalid, reset it to today
    if (!this.userData.startDate || new Date(this.userData.startDate) > now) {
      this.userData.startDate = now.toISOString();
      this.userData.currentDay = 1;
      console.log('🔧 Reset start date to today');
    } else {
      // Recalculate current day based on start date
      const startDate = new Date(this.userData.startDate);
      this.userData.currentDay = Math.floor((now - startDate) / (1000 * 60 * 60 * 24)) + 1;
      console.log(`🔧 Recalculated current day: ${this.userData.currentDay}`);
    }

    // Reset universal puff tracking data
    this.userData.totalPuffsToday = 0;
    this.userData.puffTimes = [];
    this.userData.lastPuffTime = null;

    // Reset daily counters based on tracking mode
    if (this.userData.trackingMode === 'reduction') {
      this.userData.sessionsLogged = 0;
      this.userData.sessionTimes = [];
      this.userData.lastSessionTime = null;
      this.userData.dailyAllowance = this.calculateDailyAllowance();
    } else {
      this.userData.breaksRemaining = this.userData.totalBreaks;
      this.userData.breaksTaken = 0;
    }

    // Update daily puff target
    this.updateDailyPuffTarget();

    // Clear timer-related data
    localStorage.removeItem('byevape-last-break-time');
    localStorage.removeItem('byevape-next-break-time');

    // Save corrected data
    const today = now.toDateString();
    const todayISO = now.toISOString().split('T')[0];
    localStorage.setItem('byevape-last-reset', today);
    localStorage.setItem('byevape-last-reset-iso', todayISO);
    localStorage.setItem('byevape-last-reset-timestamp', now.toISOString());

    this.saveUserData();
    this.recordDailyStats();

    this.showAlert('Data integrity issue detected and corrected.', 'info');
    console.log('✅ Corrective reset completed');
  }

  applyProgressiveReduction() {
    console.log('🔄 Applying progressive reduction...');

    const daysPassed = this.userData.currentDay;
    const reductionSchedule = {
      'fast': 30,    // 30 days total
      'normal': 60,  // 60 days total
      'relaxed': 90  // 90 days total
    };

    const totalDays = reductionSchedule[this.userData.cessationSpeed];
    const originalBreaks = this.userData.dailyVapingCount;

    console.log(`📊 Day ${daysPassed} of ${totalDays} (${this.userData.cessationSpeed} mode)`);

    // Staggered reduction - reduce every week instead of daily
    const weeksPassed = Math.floor((daysPassed - 1) / 7);
    const totalWeeks = Math.ceil(totalDays / 7);

    // Calculate reduction percentage based on cessation speed
    let reductionPerWeek;
    switch (this.userData.cessationSpeed) {
      case 'fast':
        reductionPerWeek = 0.15; // 15% per week (aggressive)
        break;
      case 'normal':
        reductionPerWeek = 0.10; // 10% per week (balanced)
        break;
      case 'relaxed':
        reductionPerWeek = 0.07; // 7% per week (gentle)
        break;
      default:
        reductionPerWeek = 0.10;
    }

    // Calculate total reduction
    const totalReduction = Math.min(weeksPassed * reductionPerWeek, 0.95); // Max 95% reduction
    const newBreakCount = Math.max(1, Math.floor(originalBreaks * (1 - totalReduction)));

    console.log(`📉 Week ${weeksPassed + 1}/${totalWeeks}: ${(totalReduction * 100).toFixed(1)}% reduction`);

    if (newBreakCount !== this.userData.totalBreaks) {
      const oldBreakCount = this.userData.totalBreaks;
      this.userData.totalBreaks = newBreakCount;
      this.userData.breaksRemaining = newBreakCount;

      // Show motivational message based on progress
      const progressPercent = (daysPassed / totalDays) * 100;
      let message = `🎯 Progress update! Daily breaks reduced from ${oldBreakCount} to ${newBreakCount}. `;

      if (progressPercent <= 25) {
        message += 'You\'re building momentum!';
      } else if (progressPercent <= 50) {
        message += 'Halfway there - amazing progress!';
      } else if (progressPercent <= 75) {
        message += 'You\'re in the final stretch!';
      } else {
        message += 'Almost free - you\'re incredible!';
      }

      this.showAlert(message, 'success');
      console.log(`✅ Breaks reduced: ${oldBreakCount} → ${newBreakCount}`);
    } else {
      console.log(`ℹ️ No reduction needed - staying at ${newBreakCount} breaks`);
    }
  }
}

// ===================================
// GLOBAL FUNCTIONS (for HTML onclick)
// ===================================

let app;

function nextOnboardingStep() {
  console.log('🔄 Global nextOnboardingStep called');
  if (!app) {
    console.error('❌ App not initialized yet');
    return;
  }
  app.nextOnboardingStep();
}

function selectTrackingMode(mode) {
  console.log('🔄 Global selectTrackingMode called with:', mode);
  if (!app) {
    console.error('❌ App not initialized yet');
    return;
  }
  app.selectTrackingMode(mode);
}

function selectCessationSpeed(speed) {
  console.log('🔄 Global selectCessationSpeed called with:', speed);
  if (!app) {
    console.error('❌ App not initialized yet');
    return;
  }
  app.selectCessationSpeed(speed);
}

function completeOnboarding() {
  console.log('🔄 Global completeOnboarding called');
  if (!app) {
    console.error('❌ App not initialized yet');
    return;
  }
  app.completeOnboarding();
}

function logPuffs() {
  console.log('🔄 Global logPuffs called');
  if (!app) {
    console.error('❌ App not initialized yet');
    return;
  }

  const puffInput = document.getElementById('puff-input');
  const count = parseInt(puffInput.value) || 1;

  if (app.logPuffs(count)) {
    // Reset input to 1
    puffInput.value = '1';
    // Update tracker display
    app.updateTrackerData();
  }
}

function syncDevicePuffs() {
  console.log('🔄 Global syncDevicePuffs called');
  if (!app) {
    console.error('❌ App not initialized yet');
    return;
  }

  const deviceInput = document.getElementById('device-puff-input');
  const deviceValue = parseInt(deviceInput.value);

  if (!deviceValue || deviceValue < 0) {
    app.showAlert('Please enter a valid device puff count.', 'warning');
    return;
  }

  if (app.logDevicePuffs(deviceValue)) {
    // Clear input after successful sync
    deviceInput.value = '';
    // Update tracker display
    app.updateTrackerData();
  }
}

// Legacy function for backward compatibility
function logVapingSession() {
  console.log('🔄 Global logVapingSession called (legacy)');
  return logPuffs();
}

function quickAddPuffs(count) {
  console.log(`🔄 Global quickAddPuffs called with count: ${count}`);
  if (!app) {
    console.error('❌ App not initialized yet');
    return;
  }

  if (app.logPuffs(count)) {
    console.log(`✅ Quick added ${count} puff(s)`);
    // Update tracker display
    app.updateTrackerData();
  }
}

// Legacy function for backward compatibility
function quickAddSessions(count) {
  console.log(`🔄 Global quickAddSessions called (legacy) with count: ${count}`);
  return quickAddPuffs(count);
}

function undoLastPuff() {
  console.log('🔄 Global undoLastPuff called');
  if (!app) {
    console.error('❌ App not initialized yet');
    return;
  }

  if (app.undoLastPuff()) {
    console.log('✅ Last puff undone');
    // Update tracker display
    app.updateTrackerData();
  }
}

// Legacy function for backward compatibility
function undoLastSession() {
  console.log('🔄 Global undoLastSession called (legacy)');
  return undoLastPuff();
}

function saveSettings() {
  app.saveSettings();
}

function resetApp() {
  app.resetApp();
}

function exportData() {
  app.exportData();
}

function importData() {
  app.showAlert('Import functionality coming soon!', 'info');
}

// ===================================
// CAPACITOR INTEGRATION
// ===================================

// Hide splash screen function
async function hideSplashScreen() {
  try {
    console.log('🔄 Hiding splash screen...');
    if (window.Capacitor && SplashScreen) {
      await SplashScreen.hide();
      console.log('✅ Splash screen hidden successfully');
    } else {
      console.log('ℹ️ Running in browser - no splash screen to hide');
    }
  } catch (error) {
    console.error('❌ Error hiding splash screen:', error);
  }
}

// ===================================
// APP INITIALIZATION
// ===================================

// Wait for both DOM and Capacitor to be ready
async function initializeApp() {
  try {
    app = new AppState();

    // Add event listeners for tracker buttons
    const takeBreakBtn = document.getElementById('take-break-btn');
    const earlyBreakBtn = document.getElementById('early-break-btn');

    if (takeBreakBtn) {
      takeBreakBtn.addEventListener('click', () => app.takeVapingBreak());
    }

    if (earlyBreakBtn) {
      earlyBreakBtn.addEventListener('click', () => app.takeEarlyBreak());
    }

    // Add event listener for sleep duration input
    const sleepInput = document.getElementById('sleep-duration');
    const activeHoursInput = document.getElementById('active-hours');

    if (sleepInput && activeHoursInput) {
      sleepInput.addEventListener('input', (e) => {
        const sleepHours = parseInt(e.target.value) || 6;
        const activeHours = 24 - sleepHours;
        activeHoursInput.value = activeHours;
        console.log(`💤 Sleep: ${sleepHours}h, Active: ${activeHours}h`);
      });
    }

    console.log('ByeVape app initialized successfully');

    // Ensure global functions are available
    window.nextOnboardingStep = nextOnboardingStep;
    window.selectTrackingMode = selectTrackingMode;
    window.selectCessationSpeed = selectCessationSpeed;
    window.completeOnboarding = completeOnboarding;
    window.logPuffs = logPuffs;
    window.syncDevicePuffs = syncDevicePuffs;
    window.quickAddPuffs = quickAddPuffs;
    window.undoLastPuff = undoLastPuff;
    window.logVapingSession = logVapingSession; // Legacy
    window.quickAddSessions = quickAddSessions; // Legacy
    window.undoLastSession = undoLastSession; // Legacy
    window.saveSettings = saveSettings;
    window.resetApp = resetApp;
    window.exportData = exportData;
    window.importData = importData;

    console.log('✅ Global functions attached to window');

    // Hide splash screen after app is fully loaded
    await hideSplashScreen();
  } catch (error) {
    console.error('Error initializing ByeVape app:', error);
    // Hide splash screen even if there's an error
    await hideSplashScreen();
  }
}

// Check if we're running in Capacitor or web browser
if (window.Capacitor) {
  // Running in Capacitor - wait for deviceready
  document.addEventListener('deviceready', initializeApp, false);
} else {
  // Running in web browser - wait for DOMContentLoaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
  } else {
    // DOM is already loaded
    initializeApp();
  }
}
