/**
 * ByeVape Structured Logging System
 * Provides consistent logging with different levels and contexts
 */

class Logger {
  constructor() {
    this.logLevel = this.getLogLevel();
    this.logHistory = [];
    this.maxHistorySize = 1000;
    this.isProduction = this.isProductionEnvironment();
    
    console.log('📝 Logger initialized with level:', this.logLevel);
  }

  /**
   * Log-Level aus Environment oder localStorage bestimmen
   */
  getLogLevel() {
    // Check localStorage first
    const savedLevel = localStorage.getItem('byevape-log-level');
    if (savedLevel) {
      return savedLevel;
    }

    // Default based on environment
    return this.isProductionEnvironment() ? 'warn' : 'debug';
  }

  /**
   * Produktionsumgebung erkennen
   */
  isProductionEnvironment() {
    return location.hostname !== 'localhost' && 
           location.hostname !== '127.0.0.1' && 
           !location.hostname.includes('dev');
  }

  /**
   * Log-Level setzen
   */
  setLogLevel(level) {
    const validLevels = ['debug', 'info', 'warn', 'error'];
    if (validLevels.includes(level)) {
      this.logLevel = level;
      localStorage.setItem('byevape-log-level', level);
      console.log('📝 Log level changed to:', level);
    }
  }

  /**
   * Prüfen ob Log-Level aktiv ist
   */
  shouldLog(level) {
    const levels = { debug: 0, info: 1, warn: 2, error: 3 };
    return levels[level] >= levels[this.logLevel];
  }

  /**
   * Log-Eintrag erstellen
   */
  createLogEntry(level, message, context = {}, error = null) {
    const timestamp = new Date().toISOString();
    const entry = {
      timestamp,
      level,
      message,
      context,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : null,
      url: window.location.href,
      userAgent: navigator.userAgent.substring(0, 100) // Truncate for storage
    };

    // Add to history
    this.logHistory.push(entry);
    if (this.logHistory.length > this.maxHistorySize) {
      this.logHistory.shift();
    }

    return entry;
  }

  /**
   * Debug-Level Logging
   */
  debug(message, context = {}) {
    if (!this.shouldLog('debug')) return;

    const entry = this.createLogEntry('debug', message, context);
    console.debug(`🔍 [DEBUG] ${message}`, context);
    
    return entry;
  }

  /**
   * Info-Level Logging
   */
  info(message, context = {}) {
    if (!this.shouldLog('info')) return;

    const entry = this.createLogEntry('info', message, context);
    console.info(`ℹ️ [INFO] ${message}`, context);
    
    return entry;
  }

  /**
   * Warning-Level Logging
   */
  warn(message, context = {}) {
    if (!this.shouldLog('warn')) return;

    const entry = this.createLogEntry('warn', message, context);
    console.warn(`⚠️ [WARN] ${message}`, context);
    
    return entry;
  }

  /**
   * Error-Level Logging
   */
  error(message, error = null, context = {}) {
    if (!this.shouldLog('error')) return;

    const entry = this.createLogEntry('error', message, context, error);
    console.error(`❌ [ERROR] ${message}`, error || '', context);
    
    // In production, consider sending to error tracking service
    if (this.isProduction) {
      this.reportError(entry);
    }
    
    return entry;
  }

  /**
   * Performance-Logging
   */
  performance(operation, duration, context = {}) {
    const message = `${operation} completed in ${duration}ms`;
    const perfContext = { ...context, duration, operation };
    
    if (duration > 1000) {
      this.warn(`Slow operation: ${message}`, perfContext);
    } else {
      this.debug(message, perfContext);
    }
  }

  /**
   * User-Action Logging
   */
  userAction(action, details = {}) {
    this.info(`User action: ${action}`, {
      action,
      details,
      timestamp: Date.now()
    });
  }

  /**
   * State-Change Logging
   */
  stateChange(path, oldValue, newValue) {
    this.debug(`State changed: ${path}`, {
      path,
      oldValue,
      newValue,
      timestamp: Date.now()
    });
  }

  /**
   * API-Call Logging
   */
  apiCall(method, url, status, duration) {
    const message = `${method} ${url} - ${status} (${duration}ms)`;
    const context = { method, url, status, duration };
    
    if (status >= 400) {
      this.error(`API Error: ${message}`, null, context);
    } else if (duration > 2000) {
      this.warn(`Slow API: ${message}`, context);
    } else {
      this.debug(`API Call: ${message}`, context);
    }
  }

  /**
   * Fehler an Tracking-Service senden (Produktionsumgebung)
   */
  reportError(entry) {
    try {
      // Hier könnte Integration mit Sentry, LogRocket, etc. erfolgen
      // Für jetzt nur localStorage für Debugging
      const errorReports = JSON.parse(localStorage.getItem('byevape-error-reports') || '[]');
      errorReports.push(entry);
      
      // Nur die letzten 50 Fehler behalten
      if (errorReports.length > 50) {
        errorReports.splice(0, errorReports.length - 50);
      }
      
      localStorage.setItem('byevape-error-reports', JSON.stringify(errorReports));
    } catch (reportError) {
      console.error('Failed to report error:', reportError);
    }
  }

  /**
   * Log-Historie abrufen
   */
  getHistory(level = null, limit = 100) {
    let history = this.logHistory;
    
    if (level) {
      history = history.filter(entry => entry.level === level);
    }
    
    return history.slice(-limit);
  }

  /**
   * Log-Historie exportieren
   */
  exportLogs() {
    try {
      const exportData = {
        logs: this.logHistory,
        exportDate: new Date().toISOString(),
        logLevel: this.logLevel,
        environment: this.isProduction ? 'production' : 'development'
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `byevape-logs-${new Date().toISOString().split('T')[0]}.json`;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
      
      this.info('Logs exported successfully');
      return true;
      
    } catch (error) {
      this.error('Failed to export logs', error);
      return false;
    }
  }

  /**
   * Log-Historie löschen
   */
  clearHistory() {
    this.logHistory = [];
    localStorage.removeItem('byevape-error-reports');
    this.info('Log history cleared');
  }

  /**
   * Performance-Messung starten
   */
  startPerformance(operation) {
    const startTime = performance.now();
    
    return {
      end: () => {
        const duration = performance.now() - startTime;
        this.performance(operation, Math.round(duration));
        return duration;
      }
    };
  }

  /**
   * Try-Catch Wrapper mit automatischem Logging
   */
  async safeExecute(operation, context = {}) {
    const perf = this.startPerformance(context.operation || 'unknown operation');
    
    try {
      this.debug(`Starting operation: ${context.operation || 'unknown'}`, context);
      const result = await operation();
      this.debug(`Operation completed: ${context.operation || 'unknown'}`, context);
      return { success: true, result };
      
    } catch (error) {
      this.error(`Operation failed: ${context.operation || 'unknown'}`, error, context);
      return { success: false, error };
      
    } finally {
      perf.end();
    }
  }

  /**
   * Debug-Informationen für Support
   */
  getDebugInfo() {
    return {
      logLevel: this.logLevel,
      isProduction: this.isProduction,
      historySize: this.logHistory.length,
      lastErrors: this.getHistory('error', 5),
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      localStorage: {
        available: this.isLocalStorageAvailable(),
        usage: this.getLocalStorageUsage()
      }
    };
  }

  /**
   * localStorage-Verfügbarkeit prüfen
   */
  isLocalStorageAvailable() {
    try {
      const test = 'test';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      return false;
    }
  }

  /**
   * localStorage-Nutzung berechnen
   */
  getLocalStorageUsage() {
    try {
      let total = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          total += localStorage[key].length + key.length;
        }
      }
      return `${(total / 1024).toFixed(2)} KB`;
    } catch (e) {
      return 'unknown';
    }
  }
}

// Globale Logger-Instanz erstellen
const logger = new Logger();

// Globale Fehlerbehandlung
window.addEventListener('error', (event) => {
  logger.error('Uncaught error', event.error, {
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  });
});

window.addEventListener('unhandledrejection', (event) => {
  logger.error('Unhandled promise rejection', event.reason, {
    type: 'unhandledrejection'
  });
});

// Export für ES6 Module
export default logger;

// Globale Verfügbarkeit
window.logger = logger;
