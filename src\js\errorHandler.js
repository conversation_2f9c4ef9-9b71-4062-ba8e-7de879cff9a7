/**
 * ByeVape Error Handler
 * Zentrale Fehlerbehandlung und Recovery-Mechanismen
 */

class ErrorHandler {
  constructor(logger) {
    this.logger = logger;
    this.errorCounts = new Map();
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second
    this.isInitialized = false;
    
    this.logger.info('🛡️ ErrorHandler initialized');
  }

  /**
   * Error Handler initialisieren
   */
  initialize() {
    try {
      // Setup global error handlers
      this.setupGlobalErrorHandlers();
      
      // Setup performance monitoring
      this.setupPerformanceMonitoring();
      
      this.isInitialized = true;
      this.logger.info('✅ ErrorHandler initialization complete');
      
    } catch (error) {
      this.logger.error('Failed to initialize <PERSON><PERSON>r<PERSON>and<PERSON>', error);
    }
  }

  /**
   * Globale Fehlerbehandlung einrichten
   */
  setupGlobalErrorHandlers() {
    // JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleJavaScriptError(event);
    });

    // Promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handlePromiseRejection(event);
    });

    // Resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.handleResourceError(event);
      }
    }, true);
  }

  /**
   * Performance-Monitoring einrichten
   */
  setupPerformanceMonitoring() {
    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) { // Tasks longer than 50ms
              this.logger.warn('Long task detected', {
                duration: entry.duration,
                startTime: entry.startTime,
                name: entry.name
              });
            }
          }
        });
        
        observer.observe({ entryTypes: ['longtask'] });
      } catch (error) {
        this.logger.debug('PerformanceObserver not supported or failed to setup');
      }
    }
  }

  /**
   * JavaScript-Fehler behandeln
   */
  handleJavaScriptError(event) {
    const error = {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error,
      stack: event.error?.stack
    };

    this.logger.error('JavaScript error occurred', event.error, error);
    
    // Track error frequency
    const errorKey = `${error.filename}:${error.lineno}`;
    this.incrementErrorCount(errorKey);
    
    // Show user-friendly message for critical errors
    if (this.isCriticalError(event.error)) {
      this.showCriticalErrorMessage(event.error);
    }
  }

  /**
   * Promise-Rejection behandeln
   */
  handlePromiseRejection(event) {
    this.logger.error('Unhandled promise rejection', event.reason, {
      type: 'unhandledrejection',
      reason: event.reason
    });

    // Prevent default browser behavior
    event.preventDefault();
    
    // Show user notification for important rejections
    if (this.isImportantRejection(event.reason)) {
      this.showErrorNotification('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.');
    }
  }

  /**
   * Resource-Loading-Fehler behandeln
   */
  handleResourceError(event) {
    const resource = {
      type: event.target.tagName,
      src: event.target.src || event.target.href,
      message: 'Resource failed to load'
    };

    this.logger.error('Resource loading error', null, resource);
    
    // Attempt to reload critical resources
    if (this.isCriticalResource(event.target)) {
      this.attemptResourceReload(event.target);
    }
  }

  /**
   * Fehler-Häufigkeit verfolgen
   */
  incrementErrorCount(errorKey) {
    const count = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, count + 1);
    
    // Alert if error occurs too frequently
    if (count >= 5) {
      this.logger.error('Frequent error detected', null, {
        errorKey,
        count: count + 1
      });
    }
  }

  /**
   * Kritischen Fehler erkennen
   */
  isCriticalError(error) {
    if (!error) return false;
    
    const criticalPatterns = [
      /cannot read property/i,
      /is not a function/i,
      /network error/i,
      /failed to fetch/i,
      /quota.*exceeded/i
    ];
    
    return criticalPatterns.some(pattern => 
      pattern.test(error.message || error.toString())
    );
  }

  /**
   * Wichtige Promise-Rejection erkennen
   */
  isImportantRejection(reason) {
    if (!reason) return false;
    
    const importantPatterns = [
      /network/i,
      /fetch/i,
      /storage/i,
      /quota/i
    ];
    
    const reasonStr = reason.message || reason.toString();
    return importantPatterns.some(pattern => pattern.test(reasonStr));
  }

  /**
   * Kritische Ressource erkennen
   */
  isCriticalResource(element) {
    const criticalResources = [
      'js/app.js',
      'js/stateManager.js',
      'css/style.css'
    ];
    
    const src = element.src || element.href || '';
    return criticalResources.some(resource => src.includes(resource));
  }

  /**
   * Ressource-Reload versuchen
   */
  async attemptResourceReload(element) {
    const maxAttempts = 3;
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      attempts++;
      
      try {
        await this.delay(this.retryDelay * attempts);
        
        // Create new element to reload
        const newElement = element.cloneNode(true);
        newElement.addEventListener('load', () => {
          this.logger.info('Resource reloaded successfully', {
            src: element.src || element.href,
            attempts
          });
        });
        
        newElement.addEventListener('error', () => {
          this.logger.warn('Resource reload failed', {
            src: element.src || element.href,
            attempts
          });
        });
        
        element.parentNode.replaceChild(newElement, element);
        break;
        
      } catch (error) {
        this.logger.error('Resource reload attempt failed', error, {
          attempts,
          maxAttempts
        });
      }
    }
  }

  /**
   * Kritische Fehlermeldung anzeigen
   */
  showCriticalErrorMessage(error) {
    const message = this.getUserFriendlyErrorMessage(error);
    
    // Create error modal
    const modal = document.createElement('div');
    modal.className = 'error-modal';
    modal.innerHTML = `
      <div class="error-modal-content">
        <h3>Unerwarteter Fehler</h3>
        <p>${message}</p>
        <div class="error-modal-actions">
          <button onclick="location.reload()" class="btn btn-primary">
            Seite neu laden
          </button>
          <button onclick="this.closest('.error-modal').remove()" class="btn btn-secondary">
            Schließen
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (modal.parentNode) {
        modal.remove();
      }
    }, 10000);
  }

  /**
   * Benutzerfreundliche Fehlermeldung generieren
   */
  getUserFriendlyErrorMessage(error) {
    if (!error) return 'Ein unbekannter Fehler ist aufgetreten.';
    
    const message = error.message || error.toString();
    
    // Map technical errors to user-friendly messages
    const errorMappings = {
      'network error': 'Netzwerkverbindung unterbrochen. Bitte prüfen Sie Ihre Internetverbindung.',
      'failed to fetch': 'Daten konnten nicht geladen werden. Bitte versuchen Sie es erneut.',
      'quota.*exceeded': 'Speicherplatz voll. Bitte löschen Sie alte Daten oder verwenden Sie einen anderen Browser.',
      'is not a function': 'Ein interner Fehler ist aufgetreten. Bitte laden Sie die Seite neu.',
      'cannot read property': 'Ein Datenfehler ist aufgetreten. Bitte laden Sie die Seite neu.'
    };
    
    for (const [pattern, friendlyMessage] of Object.entries(errorMappings)) {
      if (new RegExp(pattern, 'i').test(message)) {
        return friendlyMessage;
      }
    }
    
    return 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut oder laden Sie die Seite neu.';
  }

  /**
   * Fehler-Benachrichtigung anzeigen
   */
  showErrorNotification(message, type = 'error') {
    // Use existing notification system if available
    if (window.appState && window.appState.uiRenderer) {
      window.appState.uiRenderer.showStatusMessage(message, type);
      return;
    }
    
    // Fallback notification
    const notification = document.createElement('div');
    notification.className = `error-notification error-notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }

  /**
   * Retry-Mechanismus mit exponential backoff
   */
  async retry(operation, context = {}) {
    const maxRetries = context.maxRetries || this.maxRetries;
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.debug(`Retry attempt ${attempt}/${maxRetries}`, context);
        const result = await operation();
        
        if (attempt > 1) {
          this.logger.info(`Operation succeeded on attempt ${attempt}`, context);
        }
        
        return { success: true, result };
        
      } catch (error) {
        lastError = error;
        this.logger.warn(`Attempt ${attempt} failed`, error, context);
        
        if (attempt < maxRetries) {
          const delay = this.retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
          await this.delay(delay);
        }
      }
    }
    
    this.logger.error(`All retry attempts failed`, lastError, context);
    return { success: false, error: lastError };
  }

  /**
   * Delay-Hilfsfunktion
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Fehlerstatistiken abrufen
   */
  getErrorStats() {
    return {
      totalErrors: Array.from(this.errorCounts.values()).reduce((sum, count) => sum + count, 0),
      uniqueErrors: this.errorCounts.size,
      frequentErrors: Array.from(this.errorCounts.entries())
        .filter(([key, count]) => count >= 3)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
    };
  }

  /**
   * Fehlerstatistiken zurücksetzen
   */
  resetErrorStats() {
    this.errorCounts.clear();
    this.logger.info('Error statistics reset');
  }
}

// Export für ES6 Module
export default ErrorHandler;

// Globale Verfügbarkeit
window.ErrorHandler = ErrorHandler;
