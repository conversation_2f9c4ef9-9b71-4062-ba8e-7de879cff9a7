{"name": "byevape", "version": "1.0.0", "description": "ByeVape - Vaping Cessation Support App", "type": "module", "keywords": ["capacitor", "mobile", "vaping", "cessation", "health"], "scripts": {"start": "vite", "start:https": "cross-env VITE_HTTPS=true vite", "build": "vite build", "preview": "vite preview", "preview:https": "cross-env VITE_HTTPS=true vite preview"}, "dependencies": {"@capacitor/android": "^7.3.0", "@capacitor/camera": "latest", "@capacitor/core": "latest", "@capacitor/splash-screen": "^7.0.1"}, "devDependencies": {"@capacitor/cli": "latest", "cross-env": "^7.0.3", "vite": "^5.4.2"}, "author": "", "license": "ISC"}