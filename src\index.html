<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <title>ByeVape - Vaping Cessation Support</title>
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="description" content="ByeVape helps you quit vaping gradually with a supportive tracking system" />

    <script
      type="module"
      src="https://unpkg.com/@ionic/pwa-elements@latest/dist/ionicpwaelements/ionicpwaelements.esm.js"
    ></script>
    <script
      nomodule
      src="https://unpkg.com/@ionic/pwa-elements@latest/dist/ionicpwaelements/ionicpwaelements.js"
    ></script>

    <link rel="icon" type="image/x-icon" href="./assets/icon/favicon.ico" />
    <link rel="manifest" href="./manifest.json" />
    <link rel="stylesheet" href="./css/style.css" />
    <meta name="theme-color" content="#3b2d56" />
  </head>
  <body>
    <div class="app-layout">
      <!-- Main Content Area -->
      <main class="main-content" id="main-content">

        <!-- Onboarding Page -->
        <div class="page" id="onboarding-page" style="display: none;">
          <div class="container">
            <div class="text-center mb-4">
              <h1>Welcome to ByeVape</h1>
              <p class="text-secondary">Let's set up your personalized quit plan</p>
            </div>

            <div class="card">
              <div class="card-body">
                <div id="onboarding-step-1" class="onboarding-step">
                  <h3 class="text-center mb-4">Choose your tracking method</h3>
                  <p class="text-center text-secondary mb-4">
                    How would you like to track your vaping reduction journey?
                  </p>
                  <div class="d-flex flex-column gap-3">
                    <button class="btn btn-outline tracking-mode-btn" data-mode="reduction" onclick="selectTrackingMode('reduction')">
                      <div class="text-center">
                        <strong>📉 Session Tracking</strong><br>
                        <span class="text-primary">Gradual Reduction</span><br>
                        <small class="text-secondary">Log each vaping session and gradually reduce over time</small>
                      </div>
                    </button>
                    <button class="btn btn-outline tracking-mode-btn" data-mode="breaks" onclick="selectTrackingMode('breaks')">
                      <div class="text-center">
                        <strong>⏰ Break Timer</strong><br>
                        <span class="text-primary">Scheduled Breaks</span><br>
                        <small class="text-secondary">Take vaping breaks at timed intervals (classic mode)</small>
                      </div>
                    </button>
                  </div>
                  <button class="btn btn-primary btn-block mt-4 disabled" id="continue-step-1"
                          onclick="nextOnboardingStep()" disabled>
                    Continue →
                  </button>
                </div>

                <div id="onboarding-step-2" class="onboarding-step" style="display: none;">
                  <h3 class="text-center mb-4">How many times do you vape per day?</h3>
                  <p class="text-center text-secondary mb-4">
                    Be honest - this helps us create your personalized quit plan.
                  </p>
                  <div class="form-group">
                    <label class="form-label">Daily vaping sessions</label>
                    <input type="number" id="daily-vaping-count" class="form-control"
                           placeholder="Enter number (e.g., 20)" min="1" max="100"
                           autocomplete="off" required>
                    <small class="text-secondary">Enter a number between 1 and 100</small>
                  </div>
                  <button class="btn btn-primary btn-block" onclick="nextOnboardingStep()">
                    Continue →
                  </button>
                </div>

                <div id="onboarding-step-3" class="onboarding-step" style="display: none;">
                  <h3 class="text-center mb-4">Configure your schedule</h3>
                  <p class="text-center text-secondary mb-4">
                    Help us understand your daily routine for better tracking.
                  </p>
                  <div class="form-group">
                    <label class="form-label">Hours of sleep per night</label>
                    <input type="number" id="sleep-duration" class="form-control"
                           placeholder="6" min="4" max="12" value="6"
                           autocomplete="off" required>
                    <small class="text-secondary">We'll calculate your active hours (default: 6 hours sleep = 18 active hours)</small>
                  </div>
                  <div class="form-group">
                    <label class="form-label">Active hours per day</label>
                    <input type="number" id="active-hours" class="form-control"
                           value="18" readonly disabled>
                    <small class="text-secondary">Automatically calculated based on sleep duration</small>
                  </div>
                  <button class="btn btn-primary btn-block" onclick="nextOnboardingStep()">
                    Continue →
                  </button>
                </div>

                <div id="onboarding-step-4" class="onboarding-step" style="display: none;">
                  <h3 class="text-center mb-4">How fast do you want to quit?</h3>
                  <p class="text-center text-secondary mb-4">
                    Choose the pace that feels right for you. You can always adjust this later.
                  </p>
                  <div class="d-flex flex-column gap-3">
                    <button class="btn btn-outline cessation-speed-btn" data-speed="fast" onclick="selectCessationSpeed('fast')">
                      <div class="text-center">
                        <strong>🚀 Fast Track</strong><br>
                        <span class="text-primary">30 days</span><br>
                        <small class="text-secondary">Aggressive reduction</small>
                      </div>
                    </button>
                    <button class="btn btn-outline cessation-speed-btn" data-speed="normal" onclick="selectCessationSpeed('normal')">
                      <div class="text-center">
                        <strong>⚖️ Balanced</strong><br>
                        <span class="text-primary">60 days</span><br>
                        <small class="text-secondary">Steady progress</small>
                      </div>
                    </button>
                    <button class="btn btn-outline cessation-speed-btn" data-speed="relaxed" onclick="selectCessationSpeed('relaxed')">
                      <div class="text-center">
                        <strong>🌱 Gentle</strong><br>
                        <span class="text-primary">90 days</span><br>
                        <small class="text-secondary">Gradual reduction</small>
                      </div>
                    </button>
                  </div>
                  <button class="btn btn-primary btn-block mt-4 disabled" id="complete-onboarding"
                          onclick="completeOnboarding()" disabled>
                    🎯 Start My Journey
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tracker Page (Main Page) -->
        <div class="page" id="tracker-page">
          <div class="container">
            <!-- Page Title with Day Counter -->
            <div class="text-center">
              <h1>Tracker</h1>
              <div class="day-counter" id="day-counter">
                <span class="day-text">Day <span id="current-day-display">1</span></span>
                <span class="progress-text" id="journey-progress">of your journey</span>
              </div>
            </div>

            <!-- Break Timer Mode (Legacy) -->
            <div id="break-timer-mode" style="display: none;">
              <!-- Main Timer Display -->
              <div class="timer-container">
                <div class="progress-time" id="countdown-timer">03:45:12</div>
                <div class="progress-label">until next break</div>
              </div>

              <!-- Progress Bar -->
              <div class="progress-container">
                <div class="progress">
                  <div class="progress-bar" id="daily-progress" style="width: 60%;"></div>
                </div>
              </div>
            </div>

            <!-- Reduction Tracking Mode -->
            <div id="reduction-tracking-mode" style="display: none;">
              <!-- Circular Progress Indicator -->
              <div class="reduction-progress-container">
                <div class="circular-progress" id="reduction-progress">
                  <svg viewBox="0 0 200 200">
                    <circle class="progress-ring" cx="100" cy="100" r="90"></circle>
                    <circle class="progress-ring-fill" cx="100" cy="100" r="90" id="reduction-progress-ring"></circle>
                  </svg>
                  <div class="progress-text">
                    <div class="progress-count" id="sessions-count">0</div>
                    <div class="progress-label">of <span id="daily-allowance">0</span> today</div>
                    <div class="progress-status" id="pace-status">On track</div>
                  </div>
                </div>
              </div>

              <!-- Session Logging Interface -->
              <div class="session-logging">
                <div class="session-input-container">
                  <div class="session-input-group">
                    <input type="number" id="session-input" class="session-input"
                           placeholder="1" min="1" max="10" value="1">
                    <button class="btn btn-primary btn-lg" id="log-session-btn" onclick="logVapingSession()">
                      Log Session
                    </button>
                  </div>

                  <div class="quick-add-buttons">
                    <button class="btn btn-outline quick-add-btn" onclick="quickAddSessions(1)">+1</button>
                    <button class="btn btn-outline quick-add-btn" onclick="quickAddSessions(3)">+3</button>
                    <button class="btn btn-outline quick-add-btn" onclick="quickAddSessions(5)">+5</button>
                  </div>
                </div>

                <!-- Last Session Info -->
                <div class="last-session-info" id="last-session-info" style="display: none;">
                  <div class="last-session-text">
                    <span>Last session: <span id="last-session-time">--</span></span>
                    <button class="btn btn-sm btn-outline undo-btn" id="undo-session-btn" onclick="undoLastSession()">
                      ↩ Undo
                    </button>
                  </div>
                </div>
              </div>

              <!-- Warning/Status Messages -->
              <div class="status-message" id="status-message" style="display: none;">
                <div class="status-content">
                  <span class="status-icon" id="status-icon">ℹ️</span>
                  <span class="status-text" id="status-text">You're on track!</span>
                </div>
              </div>
            </div>

            <!-- Hidden functionality cards - keep for app logic but hide from main view -->
            <div style="display: none;">
              <!-- Breaks Remaining Card -->
              <div class="card">
                <div class="card-body text-center">
                  <h4>Breaks Remaining Today</h4>
                  <div class="d-flex justify-content-center align-items-center gap-3 mt-3">
                    <span class="text-primary" style="font-size: 2rem; font-weight: bold;" id="breaks-remaining">8</span>
                    <span class="text-secondary">of</span>
                    <span class="text-secondary" id="total-breaks">12</span>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="d-flex flex-column gap-3">
                <button class="btn btn-primary btn-lg" id="take-break-btn">
                  Take Vaping Break
                </button>
                <button class="btn btn-secondary" id="early-break-btn">
                  Early Break (adds time to next)
                </button>
              </div>

              <!-- Daily Summary -->
              <div class="card">
                <div class="card-header">
                  <h4 class="card-title">Today's Progress</h4>
                </div>
                <div class="card-body">
                  <div class="d-flex justify-content-between mb-2">
                    <span>Breaks taken:</span>
                    <span id="breaks-taken">4</span>
                  </div>
                  <div class="d-flex justify-content-between mb-2">
                    <span>Time saved:</span>
                    <span id="time-saved">2h 15m</span>
                  </div>
                  <div class="d-flex justify-content-between">
                    <span>Next reset:</span>
                    <span id="next-reset">6h 45m</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Statistics Page -->
        <div class="page" id="statistics-page" style="display: none;">
          <div class="container">
            <div class="text-center mb-4">
              <h1>Statistics</h1>
              <p class="text-secondary">Track your progress over time</p>
            </div>

            <!-- Weekly Overview -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">This Week</h3>
              </div>
              <div class="card-body">
                <!-- Break Mode Stats -->
                <div id="break-mode-stats">
                  <div class="d-flex justify-content-between mb-3">
                    <span>Average breaks per day:</span>
                    <span class="text-primary font-weight-bold" id="weekly-average">0</span>
                  </div>
                  <div class="d-flex justify-content-between mb-3">
                    <span>Best day:</span>
                    <span class="text-success" id="best-day-text">No data yet</span>
                  </div>
                  <div class="d-flex justify-content-between">
                    <span>Improvement:</span>
                    <span class="improvement-text text-secondary">No comparison data yet</span>
                  </div>
                </div>

                <!-- Reduction Mode Stats -->
                <div id="reduction-mode-stats" style="display: none;">
                  <div class="d-flex justify-content-between mb-3">
                    <span>Average sessions per day:</span>
                    <span class="text-primary font-weight-bold" id="weekly-sessions-average">0</span>
                  </div>
                  <div class="d-flex justify-content-between mb-3">
                    <span>Reduction progress:</span>
                    <span class="text-success" id="reduction-progress-text">0% complete</span>
                  </div>
                  <div class="d-flex justify-content-between mb-3">
                    <span>Daily allowance trend:</span>
                    <span class="text-info" id="allowance-trend">Decreasing</span>
                  </div>
                  <div class="d-flex justify-content-between">
                    <span>Best adherence day:</span>
                    <span class="text-success" id="best-adherence-day">No data yet</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Daily Breakdown -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Daily Breakdown</h3>
              </div>
              <div class="card-body">
                <div class="daily-stats daily-breakdown">
                  <!-- Dynamic content will be populated by JavaScript -->
                  <div class="text-center text-secondary">
                    <p>Start using the app to see your daily progress here!</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Achievements -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Achievements</h3>
              </div>
              <div class="card-body achievements-container">
                <!-- Dynamic achievements will be populated by JavaScript -->
                <div class="text-center text-secondary">
                  <p>Keep using the app to unlock achievements!</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Settings Page -->
        <div class="page" id="settings-page" style="display: none;">
          <div class="container">
            <div class="text-center mb-4">
              <h1>Settings</h1>
              <p class="text-secondary">Customize your quit plan</p>
            </div>

            <!-- Current Plan -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Current Plan</h3>
              </div>
              <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                  <span>Tracking mode:</span>
                  <span class="text-primary" id="settings-tracking-mode">Break Timer</span>
                </div>
                <div class="d-flex justify-content-between mb-2" id="daily-breaks-row">
                  <span>Daily breaks:</span>
                  <span class="text-primary" id="settings-daily-breaks">12</span>
                </div>
                <div class="d-flex justify-content-between mb-2" id="daily-allowance-row" style="display: none;">
                  <span>Today's allowance:</span>
                  <span class="text-primary" id="settings-daily-allowance">0</span>
                </div>
                <div class="d-flex justify-content-between mb-2" id="baseline-count-row" style="display: none;">
                  <span>Baseline count:</span>
                  <span class="text-primary" id="settings-baseline-count">0</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span>Cessation speed:</span>
                  <span class="text-primary" id="settings-cessation-speed">Normal (60 days)</span>
                </div>
                <div class="d-flex justify-content-between mb-2" id="sleep-duration-row" style="display: none;">
                  <span>Sleep duration:</span>
                  <span class="text-primary" id="settings-sleep-duration">6 hours</span>
                </div>
                <div class="d-flex justify-content-between">
                  <span>Days completed:</span>
                  <span class="text-primary" id="settings-days-completed">7</span>
                </div>
              </div>
            </div>

            <!-- Modify Settings -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Modify Settings</h3>
              </div>
              <div class="card-body">
                <!-- Break Mode Settings -->
                <div id="break-mode-settings">
                  <div class="form-group">
                    <label class="form-label">Daily Vaping Breaks</label>
                    <input type="number" id="modify-daily-breaks" class="form-control"
                           placeholder="Enter number" min="1" max="100">
                    <small class="text-secondary">Changes will apply from tomorrow</small>
                  </div>
                </div>

                <!-- Reduction Mode Settings -->
                <div id="reduction-mode-settings" style="display: none;">
                  <div class="form-group">
                    <label class="form-label">Sleep Duration (hours)</label>
                    <input type="number" id="modify-sleep-duration" class="form-control"
                           placeholder="6" min="4" max="12">
                    <small class="text-secondary">Affects active hours calculation</small>
                  </div>

                  <div class="form-group">
                    <label class="form-label">Warning Sensitivity</label>
                    <select id="modify-warning-sensitivity" class="form-control">
                      <option value="low">Low - Fewer warnings</option>
                      <option value="normal">Normal - Balanced warnings</option>
                      <option value="high">High - More frequent warnings</option>
                    </select>
                    <small class="text-secondary">How often you receive pace warnings</small>
                  </div>
                </div>

                <!-- Common Settings -->
                <div class="form-group">
                  <label class="form-label">Cessation Speed</label>
                  <select id="modify-cessation-speed" class="form-control">
                    <option value="fast">Fast (30 days)</option>
                    <option value="normal">Normal (60 days)</option>
                    <option value="relaxed">Relaxed (90 days)</option>
                  </select>
                </div>

                <button class="btn btn-primary btn-block" onclick="saveSettings()">
                  Save Changes
                </button>
              </div>
            </div>

            <!-- Data Management -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Data Management</h3>
              </div>
              <div class="card-body">
                <button class="btn btn-outline btn-block mb-3" onclick="exportData()">
                  Export My Data
                </button>
                <button class="btn btn-outline btn-block mb-3" onclick="importData()">
                  Import Data
                </button>
                <button class="btn btn-accent btn-block" onclick="resetApp()">
                  Reset App (Start Over)
                </button>
              </div>
            </div>

            <!-- App Information -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">About</h3>
              </div>
              <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                  <span>Version:</span>
                  <span>1.0.0</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span>Build:</span>
                  <span>2025.01.001</span>
                </div>
                <div class="text-center mt-3">
                  <p class="text-secondary">
                    ByeVape helps you quit vaping gradually with a supportive tracking system.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

      </main>

      <!-- Bottom Navigation -->
      <nav class="bottom-navigation" id="bottom-nav">
        <div class="nav-item" data-page="tracker">
          <div class="nav-icon">
            <svg width="20" height="20" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8ZM8.75 3.75C8.75 3.33579 8.41421 3 8 3C7.58579 3 7.25 3.33579 7.25 3.75V8C7.25 8.41421 7.58579 8.75 8 8.75H11.25C11.6642 8.75 12 8.41421 12 8C12 7.58579 11.6642 7.25 11.25 7.25H8.75V3.75Z" fill="currentColor"/>
            </svg>
          </div>
          <div class="nav-label">Tracker</div>
        </div>
        <div class="nav-item" data-page="statistics">
          <div class="nav-icon">
            <svg width="20" height="20" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C11.4477 2 11 2.44772 11 3V13C11 13.5523 11.4477 14 12 14H13C13.5523 14 14 13.5523 14 13V3C14 2.44772 13.5523 2 13 2H12Z" fill="currentColor"/>
              <path d="M6.5 6C6.5 5.44772 6.94772 5 7.5 5H8.5C9.05228 5 9.5 5.44772 9.5 6V13C9.5 13.5523 9.05228 14 8.5 14H7.5C6.94772 14 6.5 13.5523 6.5 13V6Z" fill="currentColor"/>
              <path d="M2 9C2 8.44772 2.44772 8 3 8H4C4.55228 8 5 8.44772 5 9V13C5 13.5523 4.55228 14 4 14H3C2.44772 14 2 13.5523 2 13V9Z" fill="currentColor"/>
            </svg>
          </div>
          <div class="nav-label">Statistics</div>
        </div>
        <div class="nav-item" data-page="settings">
          <div class="nav-icon">
            <svg width="20" height="20" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M6.45498 1.45025C6.48054 1.19465 6.69562 1 6.9525 1H9.04751C9.30438 1 9.51947 1.19465 9.54503 1.45025L9.73077 3.30766C10.2693 3.50637 10.7642 3.79513 11.1973 4.15568L12.9001 3.38721C13.1342 3.28155 13.4103 3.37049 13.5387 3.59295L14.5863 5.40728C14.7147 5.62975 14.6537 5.91334 14.4451 6.06327L12.9286 7.15339C12.9756 7.42858 13 7.71143 13 8C13 8.28864 12.9755 8.57157 12.9286 8.84682L14.4451 9.93696C14.6537 10.0869 14.7147 10.3705 14.5863 10.5929L13.5387 12.4073C13.4103 12.6297 13.1342 12.7187 12.9001 12.613L11.1971 11.8445C10.7641 12.205 10.2692 12.4937 9.73077 12.6923L9.54503 14.5498C9.51947 14.8054 9.30439 15 9.04751 15H6.9525C6.69562 15 6.48054 14.8054 6.45498 14.5498L6.26924 12.6923C5.73071 12.4936 5.23579 12.2049 4.80275 11.8443L3.09994 12.6128C2.86581 12.7185 2.58969 12.6295 2.46126 12.4071L1.41375 10.5927C1.28531 10.3703 1.34634 10.0867 1.55492 9.93673L3.07138 8.84661C3.02445 8.57142 3 8.28857 3 8C3 7.71136 3.02446 7.42843 3.07142 7.15318L1.55492 6.06304C1.34634 5.9131 1.28531 5.62951 1.41375 5.40705L2.46126 3.59272C2.58969 3.37025 2.8658 3.28131 3.09994 3.38698L4.80293 4.15552C5.23593 3.79505 5.73079 3.50634 6.26924 3.30766L6.45498 1.45025ZM6.27751 9.01699L6.25615 8.98C6.09305 8.69039 6 8.35606 6 8C6 6.89543 6.89543 6 8 6C8.73327 6 9.37437 6.39461 9.72253 6.98306L9.74383 7.01995C9.90695 7.30957 10 7.64392 10 8C10 9.10457 9.10457 10 8 10C7.26676 10 6.62567 9.60541 6.27751 9.01699Z" fill="currentColor"/>
            </svg>
          </div>
          <div class="nav-label">Settings</div>
        </div>
      </nav>
    </div>

    <script src="./js/app.js" type="module"></script>
  </body>
</html>
