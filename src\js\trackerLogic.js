/**
 * ByeVape Tracker Logic
 * Puff-Zeit-Berechnungen, Warnungen und Tracking-Logik
 */

class TrackerLogic {
  constructor(stateManager) {
    this.stateManager = stateManager;
    this.isInitialized = false;
    
    console.log('📊 TrackerLogic initialized');
  }

  /**
   * Tracker Logic initialisieren
   */
  async initialize() {
    try {
      console.log('🔄 Initializing TrackerLogic...');
      
      // Setup periodic checks
      this.setupPeriodicChecks();
      
      // Calculate initial targets
      this.updateDailyPuffTarget();
      
      this.isInitialized = true;
      console.log('✅ TrackerLogic initialized successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Error initializing TrackerLogic:', error);
      return false;
    }
  }

  /**
   * Periodische Überprüfungen einrichten
   */
  setupPeriodicChecks() {
    // Check for daily reset every minute
    setInterval(() => {
      this.checkDailyReset();
    }, 60000);

    // Update last puff time display every minute
    setInterval(() => {
      this.updateLastPuffTimeDisplay();
    }, 60000);

    // Check consumption pace every 30 minutes
    setInterval(() => {
      this.checkConsumptionPace();
    }, 30 * 60000);
  }

  /**
   * Tägliches Puff-Ziel berechnen und aktualisieren
   */
  updateDailyPuffTarget() {
    try {
      const userData = this.stateManager.getUserData();
      let target = 0;

      if (userData.trackingMode === 'breaks') {
        // Break mode: target = total breaks × average puffs per break
        target = userData.totalBreaks * userData.averagePuffsPerBreak;
      } else if (userData.trackingMode === 'reduction') {
        // Reduction mode: target = daily allowance × average puffs per session
        target = userData.dailyAllowance * userData.averagePuffsPerBreak;
      }

      this.stateManager.updateState('userData.dailyPuffTarget', target);
      console.log(`🎯 Daily puff target updated: ${target} puffs`);
      
      return target;
      
    } catch (error) {
      console.error('❌ Error updating daily puff target:', error);
      return 0;
    }
  }

  /**
   * Puff-Verbrauchstempo überprüfen
   */
  checkConsumptionPace() {
    try {
      const userData = this.stateManager.getUserData();
      const now = new Date();
      const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 6, 0); // 6 AM start
      const dayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 24 - userData.sleepDuration, 0);

      // Handle edge cases for time calculations
      let hoursElapsed;
      if (now < dayStart) {
        hoursElapsed = 0;
      } else if (now > dayEnd) {
        hoursElapsed = userData.activeHours;
      } else {
        hoursElapsed = (now - dayStart) / (1000 * 60 * 60);
      }

      const activeHours = userData.activeHours;
      const timePercentageElapsed = Math.min(hoursElapsed / activeHours, 1);
      const expectedPuffs = timePercentageElapsed * userData.dailyPuffTarget;
      const actualPuffs = userData.totalPuffsToday;

      // Calculate usage percentage
      const usagePercentage = userData.dailyPuffTarget > 0
        ? (actualPuffs / userData.dailyPuffTarget) * 100
        : 0;

      // Calculate puffs per hour rate
      const currentRate = hoursElapsed > 0 ? actualPuffs / hoursElapsed : 0;
      const targetRate = userData.dailyPuffTarget / activeHours;

      console.log(`⏰ Puff pace check: ${hoursElapsed.toFixed(1)}h elapsed, expected: ${expectedPuffs.toFixed(1)}, actual: ${actualPuffs}`);
      console.log(`📊 Puff rate: ${currentRate.toFixed(2)}/h (target: ${targetRate.toFixed(2)}/h), usage: ${usagePercentage.toFixed(1)}%`);

      return this.generatePuffWarning(actualPuffs, expectedPuffs, usagePercentage, hoursElapsed, currentRate, targetRate, timePercentageElapsed);
      
    } catch (error) {
      console.error('❌ Error checking consumption pace:', error);
      return { status: 'error', message: 'Fehler bei der Tempo-Überprüfung' };
    }
  }

  /**
   * Puff-Warnung generieren
   */
  generatePuffWarning(actualPuffs, expectedPuffs, usagePercentage, hoursElapsed, currentRate, targetRate, timePercentageElapsed) {
    try {
      const remainingPuffs = this.stateManager.getUserData().dailyPuffTarget - actualPuffs;
      const remainingHours = this.stateManager.getUserData().activeHours - hoursElapsed;

      // No puffs logged yet
      if (actualPuffs === 0) {
        if (hoursElapsed > 4) {
          return {
            status: 'great',
            message: `🎉 Ausgezeichnet! Sie haben heute ${hoursElapsed.toFixed(1)} Stunden ohne Puffs verbracht.`
          };
        } else {
          return {
            status: 'great',
            message: 'Großartiger Start! Sie haben heute noch keine Puffs genommen.'
          };
        }
      }

      // Exceeded daily target
      if (usagePercentage >= 100) {
        const excess = actualPuffs - this.stateManager.getUserData().dailyPuffTarget;
        return {
          status: 'exceeded',
          message: `🚨 Sie haben Ihr tägliches Puff-Ziel um ${excess} Puffs überschritten. Erwägen Sie eine Pause.`
        };
      }

      // Time-based contextual warnings for puffs
      const timeOfDay = this.getTimeOfDayContext(hoursElapsed);

      // Significantly ahead of pace (>50% ahead)
      if (actualPuffs > expectedPuffs * 1.5) {
        return {
          status: 'warning',
          message: this.getPuffContextualWarning('high', timeOfDay, usagePercentage, remainingPuffs, remainingHours)
        };
      }

      // Moderately ahead of pace (20-50% ahead)
      if (actualPuffs > expectedPuffs * 1.2) {
        return {
          status: 'warning',
          message: this.getPuffContextualWarning('medium', timeOfDay, usagePercentage, remainingPuffs, remainingHours)
        };
      }

      // Slightly ahead of pace (10-20% ahead)
      if (actualPuffs > expectedPuffs * 1.1) {
        return {
          status: 'caution',
          message: this.getPuffContextualWarning('low', timeOfDay, usagePercentage, remainingPuffs, remainingHours)
        };
      }

      // Behind pace (positive reinforcement)
      if (actualPuffs < expectedPuffs * 0.8) {
        return {
          status: 'great',
          message: this.getPuffPositiveReinforcement(timeOfDay, usagePercentage, remainingPuffs)
        };
      }

      // On track
      return {
        status: 'good',
        message: this.getPuffOnTrackMessage(timeOfDay, usagePercentage, remainingPuffs)
      };
      
    } catch (error) {
      console.error('❌ Error generating puff warning:', error);
      return { status: 'error', message: 'Fehler bei der Warnung-Generierung' };
    }
  }

  /**
   * Tageszeit-Kontext bestimmen
   */
  getTimeOfDayContext(hoursElapsed) {
    if (hoursElapsed < 4) {
      return 'morning';
    } else if (hoursElapsed < 8) {
      return 'midday';
    } else if (hoursElapsed < 14) {
      return 'afternoon';
    } else {
      return 'evening';
    }
  }

  /**
   * Kontextuelle Puff-Warnung generieren
   */
  getPuffContextualWarning(severity, timeOfDay, usagePercentage, remainingPuffs, remainingHours) {
    const messages = {
      high: {
        morning: `⚠️ Sie haben ${usagePercentage.toFixed(0)}% Ihrer täglichen Puffs vor Mittag genommen. Versuchen Sie zu verlangsamen.`,
        midday: `⚠️ Sie sind deutlich vor Ihrem Puff-Tempo. ${remainingPuffs} Puffs verbleiben für ${remainingHours.toFixed(1)} Stunden.`,
        afternoon: `⚠️ Hoher Puff-Verbrauch heute. Versuchen Sie sich für den Rest des Tages zu mäßigen.`,
        evening: `⚠️ Sie haben die meisten Ihrer täglichen Puffs genommen. Erwägen Sie, für heute aufzuhören.`
      },
      medium: {
        morning: `💡 Sie sind vor Ihrem morgendlichen Puff-Ziel. ${remainingPuffs} Puffs verbleiben.`,
        midday: `💡 Moderates Puff-Tempo heute. ${remainingPuffs} Puffs verbleiben für ${remainingHours.toFixed(1)} Stunden.`,
        afternoon: `💡 Sie sind leicht vor Ihrem Puff-Tempo. ${usagePercentage.toFixed(0)}% des täglichen Ziels erreicht.`,
        evening: `💡 Gutes Puff-Tempo heute. ${remainingPuffs} Puffs verbleiben.`
      },
      low: {
        morning: `ℹ️ Leicht vor dem morgendlichen Puff-Tempo. ${remainingPuffs} Puffs verbleiben heute.`,
        midday: `ℹ️ Sie haben ein gutes Puff-Tempo. ${usagePercentage.toFixed(0)}% des Ziels erreicht.`,
        afternoon: `ℹ️ Stetiger Puff-Fortschritt. ${remainingPuffs} Puffs verbleiben für heute.`,
        evening: `ℹ️ Gute Puff-Kontrolle heute. ${remainingPuffs} Puffs verbleiben.`
      }
    };

    return messages[severity][timeOfDay];
  }

  /**
   * Positive Verstärkung für gutes Puff-Tempo
   */
  getPuffPositiveReinforcement(timeOfDay, usagePercentage, remainingPuffs) {
    const messages = {
      morning: `🌟 Ausgezeichnete Puff-Kontrolle heute Morgen! ${remainingPuffs} Puffs verfügbar.`,
      midday: `🌟 Großartiges Puff-Tempo! Sie machen es besser als erwartet.`,
      afternoon: `🌟 Hervorragende Puff-Kontrolle heute! ${usagePercentage.toFixed(0)}% des Ziels verwendet.`,
      evening: `🌟 Fantastischer Tag! Sie haben große Puff-Zurückhaltung gezeigt.`
    };

    return messages[timeOfDay];
  }

  /**
   * "Auf Kurs"-Nachricht für Puff-Tempo
   */
  getPuffOnTrackMessage(timeOfDay, usagePercentage, remainingPuffs) {
    const messages = {
      morning: `✅ Perfektes morgendliches Puff-Tempo! ${remainingPuffs} Puffs verfügbar.`,
      midday: `✅ Genau auf Kurs! ${usagePercentage.toFixed(0)}% des Puff-Ziels erreicht.`,
      afternoon: `✅ Stetiger Puff-Fortschritt! ${remainingPuffs} Puffs verbleiben.`,
      evening: `✅ Ausgewogener Tag! ${remainingPuffs} Puffs verbleiben.`
    };

    return messages[timeOfDay];
  }

  /**
   * Täglichen Reset überprüfen
   */
  checkDailyReset() {
    try {
      const userData = this.stateManager.getUserData();
      const now = new Date();
      const today = now.toISOString().split('T')[0];
      
      // Check if we need to reset for a new day
      if (userData.lastResetDate !== today) {
        this.performDailyReset();
      }
      
    } catch (error) {
      console.error('❌ Error checking daily reset:', error);
    }
  }

  /**
   * Täglichen Reset durchführen
   */
  performDailyReset() {
    try {
      const userData = this.stateManager.getUserData();
      const today = new Date().toISOString().split('T')[0];
      
      console.log('🔄 Performing daily reset...');
      
      // Reset puff tracking data
      this.stateManager.updateState('userData.totalPuffsToday', 0);
      this.stateManager.updateState('userData.puffTimes', []);
      this.stateManager.updateState('userData.lastPuffTime', null);
      this.stateManager.updateState('userData.lastResetDate', today);
      
      // Update daily puff target for the new day
      this.updateDailyPuffTarget();
      
      // Increment current day
      const newDay = userData.currentDay + 1;
      this.stateManager.updateState('userData.currentDay', newDay);
      
      // Save the reset
      this.stateManager.saveUserData();
      
      console.log(`✅ Daily reset completed for day ${newDay}`);
      
    } catch (error) {
      console.error('❌ Error performing daily reset:', error);
    }
  }

  /**
   * Letzte Puff-Zeit-Anzeige aktualisieren
   */
  updateLastPuffTimeDisplay() {
    try {
      // This will trigger a UI update through the state manager
      const userData = this.stateManager.getUserData();
      if (userData.lastPuffTime) {
        // Force UI update by notifying listeners
        this.stateManager.notifyListeners('stateChanged', {
          path: 'userData.lastPuffTime',
          newValue: userData.lastPuffTime
        });
      }
      
    } catch (error) {
      console.error('❌ Error updating last puff time display:', error);
    }
  }

  /**
   * Puff-Statistiken berechnen
   */
  calculatePuffStatistics() {
    try {
      const userData = this.stateManager.getUserData();
      const dailyHistory = this.stateManager.getDailyHistory();
      
      // Calculate weekly average
      const last7Days = this.getLast7DaysData(dailyHistory);
      const weeklyAverage = last7Days.reduce((sum, day) => sum + (day.totalPuffsToday || 0), 0) / 7;
      
      // Calculate monthly average
      const last30Days = this.getLast30DaysData(dailyHistory);
      const monthlyAverage = last30Days.reduce((sum, day) => sum + (day.totalPuffsToday || 0), 0) / 30;
      
      // Calculate trend
      const trend = this.calculateTrend(last7Days);
      
      return {
        today: userData.totalPuffsToday,
        weeklyAverage: Math.round(weeklyAverage),
        monthlyAverage: Math.round(monthlyAverage),
        trend: trend,
        target: userData.dailyPuffTarget,
        remaining: Math.max(0, userData.dailyPuffTarget - userData.totalPuffsToday)
      };
      
    } catch (error) {
      console.error('❌ Error calculating puff statistics:', error);
      return null;
    }
  }

  /**
   * Daten der letzten 7 Tage abrufen
   */
  getLast7DaysData(dailyHistory) {
    const today = new Date();
    const last7Days = [];
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      last7Days.push(dailyHistory[dateStr] || { totalPuffsToday: 0 });
    }
    
    return last7Days;
  }

  /**
   * Daten der letzten 30 Tage abrufen
   */
  getLast30DaysData(dailyHistory) {
    const today = new Date();
    const last30Days = [];
    
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      last30Days.push(dailyHistory[dateStr] || { totalPuffsToday: 0 });
    }
    
    return last30Days;
  }

  /**
   * Trend berechnen (steigend, fallend, stabil)
   */
  calculateTrend(data) {
    if (data.length < 2) return 'stable';
    
    const firstHalf = data.slice(0, Math.floor(data.length / 2));
    const secondHalf = data.slice(Math.floor(data.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, day) => sum + (day.totalPuffsToday || 0), 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, day) => sum + (day.totalPuffsToday || 0), 0) / secondHalf.length;
    
    const difference = secondAvg - firstAvg;
    const threshold = firstAvg * 0.1; // 10% threshold
    
    if (difference > threshold) {
      return 'increasing';
    } else if (difference < -threshold) {
      return 'decreasing';
    } else {
      return 'stable';
    }
  }
}

// Export für ES6 Module
export default TrackerLogic;

// Globale Verfügbarkeit für Legacy-Code
window.TrackerLogic = TrackerLogic;
