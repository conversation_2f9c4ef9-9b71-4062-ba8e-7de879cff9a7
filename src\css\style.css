/* ===================================
   BYEVAPE DESIGN SYSTEM
   ================================== */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&family=Source+Sans+3:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap');

/* ===================================
   CSS CUSTOM PROPERTIES (DESIGN TOKENS)
   ================================== */

:root {
  /* Color Palette */
  --color-primary: #3b2d56;
  --color-secondary: #6e588b;
  --color-accent: #a89fc9;
  --color-light: #f3eefc;
  --color-dark: #181224;

  /* Color Variations */
  --color-primary-light: #4a3a6b;
  --color-primary-dark: #2c1f41;
  --color-secondary-light: #7d669a;
  --color-secondary-dark: #5f4a7c;
  --color-accent-light: #b7aed4;
  --color-accent-dark: #9990be;

  /* Semantic Colors */
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-error: #dc3545;
  --color-info: #17a2b8;

  /* Neutral Colors */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-gray-100: #f8f9fa;
  --color-gray-200: #e9ecef;
  --color-gray-300: #dee2e6;
  --color-gray-400: #ced4da;
  --color-gray-500: #adb5bd;
  --color-gray-600: #6c757d;
  --color-gray-700: #495057;
  --color-gray-800: #343a40;
  --color-gray-900: #212529;

  /* Typography */
  --font-family-heading: 'Playfair Display', serif;
  --font-family-body: 'Source Sans 3', sans-serif;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  --spacing-4xl: 6rem;     /* 96px */

  /* Border Radius */
  --border-radius-sm: 0.25rem;  /* 4px */
  --border-radius-md: 0.5rem;   /* 8px */
  --border-radius-lg: 1rem;     /* 16px */
  --border-radius-xl: 1.5rem;   /* 24px */
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Z-Index Scale */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Breakpoints (for reference in media queries) */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
}

/* ===================================
   BASE STYLES & RESET
   ================================== */

/* Box sizing reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margins and paddings */
* {
  margin: 0;
  padding: 0;
}

/* HTML and Body base styles */
html {
  font-size: 16px; /* Base font size for rem calculations */
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-body);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-dark);
  background-color: var(--color-light);
  min-height: 100vh;
  overflow-x: hidden;

  /* Mobile-first responsive design */
  padding: 0;
  margin: 0;

  /* Prevent horizontal scroll on mobile */
  width: 100%;
  max-width: 100vw;
}

/* Typography Base Styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-semibold);
  font-style: italic;
  line-height: var(--line-height-tight);
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-lg);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-base);
}

p {
  margin-bottom: var(--spacing-md);
  color: var(--color-dark);
}

/* Links */
a {
  color: var(--color-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover,
a:focus {
  color: var(--color-primary);
  text-decoration: underline;
}

/* Lists */
ul, ol {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-lg);
}

li {
  margin-bottom: var(--spacing-xs);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Buttons base styles */
button {
  font-family: var(--font-family-body);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

/* Form elements */
input, textarea, select {
  font-family: var(--font-family-body);
  font-size: var(--font-size-base);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: border-color var(--transition-fast);
  width: 100%;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 3px rgba(110, 88, 139, 0.1);
}

/* ===================================
   RESPONSIVE LAYOUT SYSTEM
   ================================== */

/* Container for mobile-first design */
.container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Mobile container (default) */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
    padding: 0 var(--spacing-lg);
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

/* App Layout - Portrait Only */
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--color-light);

  /* Force portrait orientation */
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
}

/* Main content area */
.main-content {
  flex: 1;
  padding: var(--spacing-md);
  padding-bottom: calc(var(--spacing-xl) + 60px); /* Account for bottom navigation */
  overflow-y: auto;
}

/* Page wrapper */
.page {
  min-height: calc(100vh - 60px); /* Account for navigation height */
  display: flex;
  flex-direction: column;
}

/* ===================================
   UTILITY CLASSES
   ================================== */

/* Display utilities */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* Flexbox utilities */
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.align-items-center { align-items: center !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.flex-1 { flex: 1 !important; }

/* Text utilities */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

/* Text colors */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-secondary) !important; }
.text-accent { color: var(--color-accent) !important; }
.text-light { color: var(--color-light) !important; }
.text-dark { color: var(--color-dark) !important; }
.text-white { color: var(--color-white) !important; }
.text-success { color: var(--color-success) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-error { color: var(--color-error) !important; }

/* Background colors */
.bg-primary { background-color: var(--color-primary) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-accent { background-color: var(--color-accent) !important; }
.bg-light { background-color: var(--color-light) !important; }
.bg-dark { background-color: var(--color-dark) !important; }
.bg-white { background-color: var(--color-white) !important; }

/* Spacing utilities */
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }
.mr-0 { margin-right: 0 !important; }

.m-1 { margin: var(--spacing-xs) !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.ml-1 { margin-left: var(--spacing-xs) !important; }
.mr-1 { margin-right: var(--spacing-xs) !important; }

.m-2 { margin: var(--spacing-sm) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.ml-2 { margin-left: var(--spacing-sm) !important; }
.mr-2 { margin-right: var(--spacing-sm) !important; }

.m-3 { margin: var(--spacing-md) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.ml-3 { margin-left: var(--spacing-md) !important; }
.mr-3 { margin-right: var(--spacing-md) !important; }

.m-4 { margin: var(--spacing-lg) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.ml-4 { margin-left: var(--spacing-lg) !important; }
.mr-4 { margin-right: var(--spacing-lg) !important; }

.m-5 { margin: var(--spacing-xl) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }
.ml-5 { margin-left: var(--spacing-xl) !important; }
.mr-5 { margin-right: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }
.pr-0 { padding-right: 0 !important; }

.p-1 { padding: var(--spacing-xs) !important; }
.pt-1 { padding-top: var(--spacing-xs) !important; }
.pb-1 { padding-bottom: var(--spacing-xs) !important; }
.pl-1 { padding-left: var(--spacing-xs) !important; }
.pr-1 { padding-right: var(--spacing-xs) !important; }

.p-2 { padding: var(--spacing-sm) !important; }
.pt-2 { padding-top: var(--spacing-sm) !important; }
.pb-2 { padding-bottom: var(--spacing-sm) !important; }
.pl-2 { padding-left: var(--spacing-sm) !important; }
.pr-2 { padding-right: var(--spacing-sm) !important; }

.p-3 { padding: var(--spacing-md) !important; }
.pt-3 { padding-top: var(--spacing-md) !important; }
.pb-3 { padding-bottom: var(--spacing-md) !important; }
.pl-3 { padding-left: var(--spacing-md) !important; }
.pr-3 { padding-right: var(--spacing-md) !important; }

/* Border radius utilities */
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-full { border-radius: var(--border-radius-full) !important; }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

/* ===================================
   COMPONENT STYLES
   ================================== */

/* Button Components */
.btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  min-height: 48px; /* Touch-friendly minimum */
  font-size: var(--font-size-base);
  line-height: 1;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Primary Button */
.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary {
  background-color: var(--color-secondary);
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-secondary-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* Accent Button */
.btn-accent {
  background-color: var(--color-accent);
  color: var(--color-dark);
  box-shadow: var(--shadow-md);
}

.btn-accent:hover:not(:disabled) {
  background-color: var(--color-accent-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* Outline Button */
.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  box-shadow: none;
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--color-primary);
  color: var(--color-white);
}

/* Large Button */
.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 56px;
  border-radius: var(--border-radius-xl);
}

/* Small Button */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 40px;
  border-radius: var(--border-radius-md);
}

/* Full Width Button */
.btn-block {
  width: 100%;
  display: flex;
}

/* Card Component */
.card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: box-shadow var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

.card-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-gray-200);
}

.card-title {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  font-style: italic;
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
}

.card-body {
  flex: 1;
}

.card-footer {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-gray-200);
}

/* Progress Bar Component */
.progress {
  width: 100%;
  height: 8px;
  background-color: var(--color-gray-200);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  margin: var(--spacing-sm) 0;
}

.progress-bar {
  height: 100%;
  background-color: var(--color-secondary);
  border-radius: var(--border-radius-full);
  transition: width var(--transition-normal);
}

.progress-bar.success {
  background-color: var(--color-success);
}

.progress-bar.warning {
  background-color: var(--color-warning);
}

.progress-bar.error {
  background-color: var(--color-error);
}

/* Circular Progress Component */
.circular-progress {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
}

.circular-progress svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circular-progress .progress-ring {
  fill: none;
  stroke: var(--color-gray-200);
  stroke-width: 8;
}

.circular-progress .progress-ring-fill {
  fill: none;
  stroke: var(--color-secondary);
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dasharray var(--transition-normal);
}

.circular-progress .progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.circular-progress .progress-time {
  font-size: var(--font-size-2xl);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.circular-progress .progress-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  font-family: var(--font-family-body);
  font-weight: var(--font-weight-normal);
  font-style: normal;
}

/* Form Components */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-weight: var(--font-weight-medium);
  color: var(--color-dark);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.form-control {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--color-gray-300);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
  background-color: var(--color-white);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 3px rgba(110, 88, 139, 0.1);
}

.form-control.error {
  border-color: var(--color-error);
}

.form-control.success {
  border-color: var(--color-success);
}

/* Alert Components */
.alert {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-md);
  border-left: 4px solid;
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  border-left-color: var(--color-success);
  color: var(--color-success);
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-left-color: var(--color-warning);
  color: #856404;
}

.alert-error {
  background-color: rgba(220, 53, 69, 0.1);
  border-left-color: var(--color-error);
  color: var(--color-error);
}

.alert-info {
  background-color: rgba(23, 162, 184, 0.1);
  border-left-color: var(--color-info);
  color: var(--color-info);
}

/* ===================================
   MOBILE-SPECIFIC STYLES
   ================================== */

/* Touch-friendly interactions */
@media (max-width: 768px) {
  /* Increase touch targets */
  .btn {
    min-height: 48px;
    padding: var(--spacing-md) var(--spacing-lg);
  }

  /* Larger text for better readability */
  body {
    font-size: var(--font-size-lg);
  }

  h1 {
    font-size: var(--font-size-3xl);
  }

  h2 {
    font-size: var(--font-size-2xl);
  }

  /* Adjust spacing for mobile */
  .main-content {
    padding: var(--spacing-sm);
  }

  .card {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
  }

  /* Larger circular progress for mobile */
  .circular-progress {
    width: 250px;
    height: 250px;
  }

  .circular-progress .progress-time {
    font-size: var(--font-size-3xl);
  }

  /* Enhanced input touch targets for mobile */
  .session-input,
  .form-control,
  input[type="number"],
  input[type="text"],
  select {
    min-height: 48px;
    padding: var(--spacing-md);
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  /* Enhanced quick-add buttons for mobile */
  .quick-add-buttons {
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
  }

  .quick-add-btn {
    min-height: 48px;
    min-width: 60px;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    -webkit-tap-highlight-color: transparent;
  }

  /* Better session input layout for mobile */
  .session-input-group {
    gap: var(--spacing-md);
  }

  .session-input-group .session-input {
    text-align: center;
    font-weight: var(--font-weight-bold);
  }

  /* Enhanced navigation for mobile */
  .bottom-navigation {
    padding: var(--spacing-sm) var(--spacing-md);
    height: 70px;
  }

  .nav-item {
    min-height: 48px;
    min-width: 48px;
    padding: var(--spacing-xs);
    -webkit-tap-highlight-color: transparent;
  }

  .nav-icon {
    width: 24px;
    height: 24px;
  }

  .nav-label {
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
  }

  /* Progress display enhancements */
  .progress-count {
    font-size: 3.5rem;
    font-weight: var(--font-weight-bold);
  }

  .progress-label {
    font-size: 1.1rem;
  }

  .progress-status {
    font-size: 1rem;
    margin-top: var(--spacing-sm);
  }
}

/* Extra small screens (phones in portrait) */
@media (max-width: 480px) {
  /* Stack elements vertically for very small screens */
  .session-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .session-input-group .btn {
    width: 100%;
  }

  .quick-add-buttons {
    justify-content: space-between;
  }

  .quick-add-btn {
    flex: 1;
    min-width: 0;
  }

  /* Adjust progress circle for small screens */
  .progress-circle {
    width: 200px;
    height: 200px;
  }

  .progress-count {
    font-size: 3rem;
  }

  /* Compact card layout */
  .card-body {
    padding: var(--spacing-md);
  }

  .card-header {
    padding: var(--spacing-md);
  }

  /* Ensure minimum touch targets even on small screens */
  .btn,
  .nav-item,
  .quick-add-btn,
  .session-input {
    min-height: 44px;
    min-width: 44px;
  }

  /* Device sync section adjustments */
  .device-sync-section {
    margin-top: var(--spacing-lg) !important;
    padding-top: var(--spacing-lg) !important;
  }

  .device-sync-section .session-input-group {
    margin-top: var(--spacing-sm) !important;
  }
}

/* Large screens optimization */
@media (min-width: 1024px) {
  .container {
    max-width: 800px;
    margin: 0 auto;
  }

  /* Hover effects for desktop */
  .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Desktop-specific layouts */
  .session-input-group {
    flex-direction: row;
    align-items: center;
  }

  .quick-add-buttons {
    justify-content: center;
  }

  /* Larger progress circle for desktop */
  .progress-circle {
    width: 300px;
    height: 300px;
  }

  .progress-count {
    font-size: 4rem;
  }
}

/* ===================================
   ACCESSIBILITY IMPROVEMENTS
   ================================== */

/* Focus styles for keyboard navigation */
*:focus {
  outline: 2px solid var(--color-secondary);
  outline-offset: 2px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #000000;
    --color-secondary: #333333;
    --color-accent: #666666;
    --color-light: #ffffff;
    --color-dark: #000000;
  }
}

/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
  :root {
    --color-light: #1a1a1a;
    --color-dark: #ffffff;
    --color-gray-100: #2d2d2d;
    --color-gray-200: #404040;
    --color-gray-300: #525252;
  }
}

/* ===================================
   NAVIGATION COMPONENTS
   ================================== */

/* Bottom Navigation */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-white);
  border-top: 1px solid var(--color-gray-200);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: var(--spacing-sm) 0;
  box-shadow: var(--shadow-lg);
  z-index: var(--z-index-fixed);
  height: 70px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: var(--border-radius-md);
  min-width: 60px;
  flex: 1;
  max-width: 100px;
}

.nav-item:hover {
  background-color: var(--color-gray-100);
}

.nav-item.active {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.nav-item.active .nav-icon {
  transform: scale(1.1);
}

.nav-icon {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-xs);
  transition: transform var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon svg {
  width: 20px;
  height: 20px;
  transition: transform var(--transition-fast);
}

.nav-item.active .nav-icon svg {
  color: var(--color-white);
}

.nav-item:not(.active) .nav-icon svg {
  color: var(--color-gray-600);
}

.nav-item.active .nav-label {
  color: var(--color-white);
}

.nav-item:not(.active) .nav-label {
  color: var(--color-gray-600);
}

.nav-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-align: center;
  line-height: 1;
}

/* Page Management */
.page {
  min-height: calc(100vh - 70px); /* Account for bottom navigation */
  animation: fadeIn var(--transition-normal) ease-in-out;
}

/* Override for tracker page to prevent scrolling */
#tracker-page.page {
  padding-bottom: 0;
  height: calc(100vh - 70px);
  max-height: calc(100vh - 70px);
}

.page.hidden {
  display: none !important;
}

/* Page Transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* ===================================
   PAGE-SPECIFIC COMPONENTS
   ================================== */

/* Onboarding Styles */
#onboarding-page {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  height: 100vh;
  overflow-y: auto;
  color: var(--color-light);
  position: relative;
  z-index: 1;
}

/* Ensure all text on onboarding page is readable */
#onboarding-page h1,
#onboarding-page h2,
#onboarding-page h3,
#onboarding-page h4,
#onboarding-page h5,
#onboarding-page h6,
#onboarding-page p,
#onboarding-page span,
#onboarding-page div {
  color: var(--color-light) !important;
}

#onboarding-page .container {
  padding: var(--spacing-xl) var(--spacing-lg);
  max-width: 400px;
  margin: 0 auto;
  min-height: calc(100vh - 2 * var(--spacing-xl));
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.onboarding-step {
  animation: slideIn var(--transition-normal) ease-in-out;
  min-height: 400px;
  padding: var(--spacing-xl);
  text-align: center;
}

.onboarding-step h3 {
  font-family: var(--font-heading);
  font-size: 2rem;
  font-weight: 600;
  color: var(--color-light);
  margin-bottom: var(--spacing-lg);
  line-height: 1.2;
}

.onboarding-step p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.5;
  margin-bottom: var(--spacing-lg);
}

/* Form styling for onboarding */
.onboarding-step .form-group {
  margin-bottom: var(--spacing-lg);
  text-align: left;
}

.onboarding-step .form-label {
  color: var(--color-light);
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  display: block;
}

.onboarding-step .form-control {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: var(--color-light);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  font-size: 1.1rem;
  transition: all var(--transition-normal);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.onboarding-step .form-control:focus {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2), 0 2px 8px rgba(0, 0, 0, 0.15);
  outline: none;
}

.onboarding-step .form-control::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.onboarding-step small {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* Cessation speed buttons */
.cessation-speed-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: var(--color-light);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-md);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cessation-speed-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--color-accent);
  transform: translateY(-2px);
}

.cessation-speed-btn.selected {
  background: var(--color-accent);
  border-color: var(--color-accent);
  color: var(--color-primary);
  font-weight: 600;
}

.cessation-speed-btn strong {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-xs);
}

.cessation-speed-btn .text-primary {
  color: var(--color-accent) !important;
}

.cessation-speed-btn.selected .text-primary {
  color: var(--color-primary) !important;
}

/* Tracking mode buttons */
.tracking-mode-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: var(--color-light);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-md);
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tracking-mode-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--color-accent);
  transform: translateY(-2px);
}

.tracking-mode-btn.selected {
  background: var(--color-accent);
  border-color: var(--color-accent);
  color: var(--color-primary);
  font-weight: 600;
}

.tracking-mode-btn strong {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-xs);
}

.tracking-mode-btn .text-primary {
  color: var(--color-accent) !important;
}

.tracking-mode-btn.selected .text-primary {
  color: var(--color-primary) !important;
}

/* Primary button styling for onboarding */
.onboarding-step .btn-primary {
  background: var(--color-accent);
  border: none;
  color: var(--color-primary);
  font-weight: 600;
  font-size: 1.1rem;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius);
  transition: all var(--transition-normal);
  min-height: 50px;
}

.onboarding-step .btn-primary:hover:not(:disabled) {
  background: var(--color-light);
  color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.onboarding-step .btn-primary:active:not(:disabled) {
  background: var(--color-secondary);
  color: var(--color-light);
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.onboarding-step .btn-primary:disabled,
.onboarding-step .btn-primary.disabled {
  background: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.6);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}



@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.cessation-speed-btn {
  padding: var(--spacing-lg);
  text-align: center;
  min-height: 80px;
}

.cessation-speed-btn.selected {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
}

/* Tracker Page Styles */
#tracker-page {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  color: var(--color-light);
  min-height: calc(100vh - 70px); /* Account for navigation height */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent vertical scrolling */
}

#tracker-page .container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly; /* Distribute space evenly */
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-md);
  max-height: calc(100vh - 70px); /* Ensure it fits within viewport */
}

#tracker-page h1 {
  color: var(--color-white) !important;
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-4xl);
  text-align: center;
  margin: 0; /* Remove default margins for better flex control */
}

#tracker-page .text-secondary {
  color: var(--color-white) !important;
}

#tracker-page .card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  color: var(--color-white);
}

#tracker-page .card-title {
  color: var(--color-white) !important;
}

#tracker-page .progress-time {
  color: var(--color-white) !important;
  font-size: 5rem;
  font-weight: var(--font-weight-bold);
  font-family: var(--font-family-heading);
  letter-spacing: 0.05em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin: 0; /* Remove default margins */
}

#tracker-page .progress-label {
  color: var(--color-white) !important;
  font-size: var(--font-size-xl);
  font-family: var(--font-family-body);
  font-weight: var(--font-weight-normal);
  margin: 0; /* Remove default margins */
}

/* Day Counter Styling */
.day-counter {
  margin-top: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.day-text {
  color: var(--color-accent);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  font-family: var(--font-family-heading);
}

.progress-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-md);
  font-family: var(--font-family-body);
  margin-left: var(--spacing-xs);
}

/* Timer display container */
#tracker-page .timer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  gap: var(--spacing-md);
}

/* Progress bar container */
#tracker-page .progress-container {
  width: 100%;
  max-width: 320px;
  margin: var(--spacing-xl) auto;
}

/* Tracker page progress bar styling */
#tracker-page .progress {
  height: 32px;
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: var(--border-radius-full);
}

#tracker-page .progress-bar {
  background: #f5e6d3 !important; /* Cream/beige color to match target design */
  border-radius: var(--border-radius-full);
}

.circular-progress {
  margin: var(--spacing-lg) auto;
}

.daily-stats .progress {
  height: 6px;
}

/* Statistics Page Styles */
.daily-stats {
  max-height: 300px;
  overflow-y: auto;
}

.daily-stats .d-flex {
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--color-gray-100);
}

.daily-stats .d-flex:last-child {
  border-bottom: none;
}

/* Settings Page Styles */
.form-group small {
  display: block;
  margin-top: var(--spacing-xs);
  font-style: italic;
}

/* ===================================
   RESPONSIVE NAVIGATION
   ================================== */

/* Tablet and larger screens */
@media (min-width: 768px) {
  .bottom-navigation {
    max-width: 400px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    border-left: 1px solid var(--color-gray-200);
    border-right: 1px solid var(--color-gray-200);
  }

  .nav-item {
    min-width: 80px;
  }

  .nav-label {
    font-size: var(--font-size-sm);
  }
}

/* Large screens - center the app */
@media (min-width: 992px) {
  .app-layout {
    max-width: 500px;
    margin: 0 auto;
    box-shadow: var(--shadow-xl);
  }

  .main-content {
    background-color: var(--color-white);
  }
}

/* ===================================
   LOADING AND STATE STYLES
   ================================== */

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-300);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Disabled state */
.disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}

/* Success state */
.success-state {
  background-color: rgba(40, 167, 69, 0.1);
  border: 2px solid var(--color-success);
  color: var(--color-success);
}

/* Warning state */
.warning-state {
  background-color: rgba(255, 193, 7, 0.1);
  border: 2px solid var(--color-warning);
  color: #856404;
}

/* Error state */
.error-state {
  background-color: rgba(220, 53, 69, 0.1);
  border: 2px solid var(--color-error);
  color: var(--color-error);
}

/* ===================================
   REDUCTION TRACKING COMPONENTS
   ================================== */

/* Reduction Progress Container */
.reduction-progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  margin: var(--spacing-xl) 0;
}

/* Enhanced Circular Progress for Reduction Mode */
#reduction-progress {
  width: 280px;
  height: 280px;
  margin: 0 auto var(--spacing-xl);
}

#reduction-progress .progress-count {
  font-size: 4rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-white);
  font-family: var(--font-family-heading);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

#reduction-progress .progress-label {
  font-size: var(--font-size-lg);
  color: rgba(255, 255, 255, 0.8);
  font-family: var(--font-family-body);
  margin-bottom: var(--spacing-xs);
}

#reduction-progress .progress-status {
  font-size: var(--font-size-sm);
  color: var(--color-accent);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Session Logging Interface */
.session-logging {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.session-input-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.session-input-group {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.session-input {
  flex: 0 0 80px;
  height: 56px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  color: var(--color-white);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  text-align: center;
  transition: all var(--transition-normal);
}

.session-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--color-accent);
  outline: none;
}

.session-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Enhanced Input validation styles */
.session-input.input-error,
.form-control.input-error {
  border-color: #ff4444 !important;
  background: rgba(255, 68, 68, 0.1) !important;
  box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.2);
  animation: shake 0.3s ease-in-out;
}

.input-error-message {
  color: #ff4444;
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 68, 68, 0.1);
  border-radius: var(--border-radius-sm);
  border-left: 3px solid #ff4444;
  animation: fadeInError 0.3s ease-in-out;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.input-error-message::before {
  content: "⚠️";
  font-size: var(--font-size-base);
}

/* Success state for inputs */
.session-input.input-success,
.form-control.input-success {
  border-color: #4CAF50 !important;
  background: rgba(76, 175, 80, 0.1) !important;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Animations */
@keyframes fadeInError {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Status message styles */
.status-message {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-md) 0;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  animation: slideInDown 0.3s ease-out;
}

.status-message.status-success {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.status-message.status-warning {
  background: rgba(255, 152, 0, 0.1);
  border: 1px solid rgba(255, 152, 0, 0.3);
  color: #FF9800;
}

.status-message.status-error {
  background: rgba(255, 68, 68, 0.1);
  border: 1px solid rgba(255, 68, 68, 0.3);
  color: #ff4444;
}

.status-message.status-info {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  color: #2196F3;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===================================
   ERROR HANDLING STYLES
   ================================== */

/* Error Modal */
.error-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.error-modal-content {
  background: var(--color-white);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: scaleIn 0.3s ease-out;
}

.error-modal-content h3 {
  color: #ff4444;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-xl);
}

.error-modal-content p {
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-lg);
  line-height: 1.5;
}

.error-modal-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

.error-modal-actions .btn {
  min-width: 120px;
}

/* Error Notifications */
.error-notification {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  color: white;
  font-weight: var(--font-weight-medium);
  z-index: 9998;
  max-width: 300px;
  animation: slideInRight 0.3s ease-out;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.error-notification-error {
  background: #ff4444;
}

.error-notification-warning {
  background: #FF9800;
}

.error-notification-info {
  background: #2196F3;
}

.error-notification-success {
  background: #4CAF50;
}

/* Critical Error Page */
.critical-error {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  text-align: center;
  padding: var(--spacing-xl);
}

.critical-error h3 {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-lg);
}

.critical-error p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
}

.critical-error button {
  background: white;
  color: #ff4444;
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  cursor: pointer;
  transition: all 0.2s ease;
}

.critical-error button:hover {
  background: #f0f0f0;
  transform: translateY(-1px);
}

/* Loading States */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1001;
}

/* Animations */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Debug Panel (Development only) */
.debug-panel {
  position: fixed;
  bottom: 80px;
  right: var(--spacing-md);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-family: monospace;
  font-size: var(--font-size-sm);
  max-width: 300px;
  z-index: 9997;
  display: none;
}

.debug-panel.visible {
  display: block;
}

.debug-panel h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: #4CAF50;
}

.debug-panel .debug-item {
  margin-bottom: var(--spacing-xs);
  padding: var(--spacing-xs);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-sm);
}

.debug-panel .debug-label {
  color: #FFC107;
  font-weight: bold;
}

.debug-panel .debug-value {
  color: #E0E0E0;
}

#log-session-btn {
  flex: 1;
  height: 56px;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  background: var(--color-accent);
  border-color: var(--color-accent);
  color: var(--color-primary);
}

#log-session-btn:hover {
  background: var(--color-accent-light);
  border-color: var(--color-accent-light);
  transform: translateY(-2px);
}

/* Quick Add Buttons */
.quick-add-buttons {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

.quick-add-btn {
  flex: 1;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: var(--color-white);
  font-weight: var(--font-weight-bold);
  transition: all var(--transition-normal);
}

.quick-add-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--color-accent);
  transform: translateY(-1px);
}

/* Last Session Info */
.last-session-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  text-align: center;
}

.last-session-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-sm);
}

.undo-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  height: auto;
  min-height: auto;
}

.undo-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--color-accent);
  color: var(--color-white);
}

/* Status Messages */
.status-message {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-top: var(--spacing-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  justify-content: center;
}

.status-icon {
  font-size: var(--font-size-lg);
}

.status-text {
  color: var(--color-white);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-md);
}

/* Status message variants */
.status-message.success {
  background: rgba(40, 167, 69, 0.2);
  border-color: var(--color-success);
}

.status-message.warning {
  background: rgba(255, 193, 7, 0.2);
  border-color: var(--color-warning);
}

.status-message.error {
  background: rgba(220, 53, 69, 0.2);
  border-color: var(--color-error);
}

.status-message.info {
  background: rgba(23, 162, 184, 0.2);
  border-color: var(--color-info);
}

/* Mobile responsiveness for reduction tracking */
@media (max-width: 768px) {
  #reduction-progress {
    width: 250px;
    height: 250px;
  }

  #reduction-progress .progress-count {
    font-size: 3.5rem;
  }

  .session-input-group {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .session-input {
    flex: none;
    width: 100%;
  }

  #log-session-btn {
    width: 100%;
  }

  .quick-add-buttons {
    gap: var(--spacing-xs);
  }
}

/* ===================================
   PORTRAIT ORIENTATION LOCK
   ================================== */

/* Force portrait orientation on mobile */
@media screen and (orientation: landscape) and (max-width: 768px) {
  .app-layout::before {
    content: "Please rotate your device to portrait mode for the best experience.";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-primary);
    color: var(--color-white);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    z-index: 9999;
    padding: var(--spacing-lg);
  }

  .main-content,
  .bottom-navigation {
    display: none;
  }
}
