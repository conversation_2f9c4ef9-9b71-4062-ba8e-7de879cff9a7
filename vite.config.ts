import { defineConfig } from 'vite';

// Check if HTTPS mode is requested via environment variable
const useHttps = process.env.VITE_HTTPS === 'true';

export default defineConfig({
  root: './src',
  build: {
    outDir: '../dist',
    minify: false,
    emptyOutDir: true,
  },
  server: {
    https: useHttps,
    host: true, // Allow external connections
    port: 3000,
    open: false // Don't auto-open browser
  },
  preview: {
    https: useHttps,
    host: true,
    port: 3001,
    open: false
  }
});
