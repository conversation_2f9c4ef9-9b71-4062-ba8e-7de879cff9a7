/**
 * ByeVape UI Renderer
 * Zentrale UI-Aktualisierungen und Rendering-Logik
 */

class UIRenderer {
  constructor(stateManager) {
    this.stateManager = stateManager;
    this.elements = new Map();
    this.isInitialized = false;
    
    console.log('🎨 UIRenderer initialized');
  }

  /**
   * UI Renderer initialisieren
   */
  async initialize() {
    try {
      console.log('🔄 Initializing UIRenderer...');
      
      // Cache DOM elements
      this.cacheElements();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Initial render
      await this.renderAll();
      
      this.isInitialized = true;
      console.log('✅ U<PERSON>enderer initialized successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Error initializing UIRenderer:', error);
      return false;
    }
  }

  /**
   * DOM-Elemente cachen für bessere Performance
   */
  cacheElements() {
    const elementIds = [
      'puffs-count',
      'daily-puff-target',
      'pace-status',
      'reduction-progress-ring',
      'last-puff-info',
      'last-puff-time',
      'puff-input',
      'device-puff-input',
      'status-message',
      'current-day',
      'days-remaining',
      'progress-percentage'
    ];

    elementIds.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        this.elements.set(id, element);
      } else {
        console.warn(`⚠️ Element not found: ${id}`);
      }
    });

    console.log(`📦 Cached ${this.elements.size} DOM elements`);
  }

  /**
   * Event-Listener einrichten
   */
  setupEventListeners() {
    // Listen to state changes
    this.stateManager.addEventListener('stateChanged', (data) => {
      this.handleStateChange(data);
    });

    this.stateManager.addEventListener('userDataSaved', () => {
      this.renderUserData();
    });

    // Window resize events for responsive updates
    window.addEventListener('resize', this.debounce(() => {
      this.handleResize();
    }, 250));
  }

  /**
   * Zustandsänderungen behandeln
   */
  handleStateChange(data) {
    try {
      const { path, newValue } = data;
      
      // Route specific updates based on path
      if (path.startsWith('userData.totalPuffsToday')) {
        this.renderPuffCount();
        this.renderProgress();
      } else if (path.startsWith('userData.dailyPuffTarget')) {
        this.renderDailyTarget();
        this.renderProgress();
      } else if (path.startsWith('userData.lastPuffTime')) {
        this.renderLastPuffInfo();
      } else if (path.startsWith('ui.statusMessage')) {
        this.renderStatusMessage();
      }
      
    } catch (error) {
      console.error('❌ Error handling state change:', error);
    }
  }

  /**
   * Fenster-Größenänderungen behandeln
   */
  handleResize() {
    try {
      // Update responsive elements
      this.updateResponsiveElements();
      
      // Recalculate progress ring if needed
      this.renderProgress();
      
    } catch (error) {
      console.error('❌ Error handling resize:', error);
    }
  }

  /**
   * Alle UI-Elemente rendern
   */
  async renderAll() {
    try {
      console.log('🎨 Rendering all UI elements...');
      
      await Promise.all([
        this.renderUserData(),
        this.renderProgress(),
        this.renderStatusMessage(),
        this.renderLastPuffInfo()
      ]);
      
      console.log('✅ All UI elements rendered');
      
    } catch (error) {
      console.error('❌ Error rendering all UI elements:', error);
    }
  }

  /**
   * Benutzerdaten rendern
   */
  async renderUserData() {
    try {
      const userData = this.stateManager.getUserData();
      
      this.renderPuffCount();
      this.renderDailyTarget();
      this.renderCurrentDay();
      this.renderProgress();
      
    } catch (error) {
      console.error('❌ Error rendering user data:', error);
    }
  }

  /**
   * Puff-Anzahl rendern
   */
  renderPuffCount() {
    try {
      const userData = this.stateManager.getUserData();
      const element = this.elements.get('puffs-count');
      
      if (element) {
        const count = userData.totalPuffsToday || 0;
        element.textContent = count;
        
        // Add animation for count changes
        element.classList.add('count-updated');
        setTimeout(() => {
          element.classList.remove('count-updated');
        }, 300);
      }
      
    } catch (error) {
      console.error('❌ Error rendering puff count:', error);
    }
  }

  /**
   * Tägliches Ziel rendern
   */
  renderDailyTarget() {
    try {
      const userData = this.stateManager.getUserData();
      const element = this.elements.get('daily-puff-target');
      
      if (element) {
        const target = userData.dailyPuffTarget || 0;
        element.textContent = target;
      }
      
    } catch (error) {
      console.error('❌ Error rendering daily target:', error);
    }
  }

  /**
   * Aktuellen Tag rendern
   */
  renderCurrentDay() {
    try {
      const userData = this.stateManager.getUserData();
      const element = this.elements.get('current-day');
      
      if (element) {
        element.textContent = userData.currentDay || 1;
      }
      
    } catch (error) {
      console.error('❌ Error rendering current day:', error);
    }
  }

  /**
   * Fortschrittsring rendern
   */
  renderProgress() {
    try {
      const userData = this.stateManager.getUserData();
      const progressRing = this.elements.get('reduction-progress-ring');
      
      if (!progressRing) return;

      // Calculate progress percentage based on puffs
      const progressPercent = userData.dailyPuffTarget > 0
        ? (userData.totalPuffsToday / userData.dailyPuffTarget) * 100
        : 0;

      // SVG circle calculations
      const radius = 90;
      const circumference = 2 * Math.PI * radius;
      const offset = circumference * (1 - (progressPercent / 100));

      // Update the progress ring
      progressRing.style.strokeDasharray = circumference;
      progressRing.style.strokeDashoffset = offset;

      // Update progress color based on status
      const strokeColor = this.getProgressColor(progressPercent);
      progressRing.style.stroke = strokeColor;
      progressRing.style.transition = 'stroke-dashoffset 0.5s ease-in-out, stroke 0.3s ease-in-out';

      // Update pace status
      this.renderPaceStatus();

      console.log(`🔄 Progress updated: ${progressPercent.toFixed(1)}% (${userData.totalPuffsToday}/${userData.dailyPuffTarget})`);
      
    } catch (error) {
      console.error('❌ Error rendering progress:', error);
    }
  }

  /**
   * Fortschrittsfarbe basierend auf Prozentsatz bestimmen
   */
  getProgressColor(progressPercent) {
    if (progressPercent >= 100) {
      return '#F44336'; // Red - exceeded
    } else if (progressPercent >= 80) {
      return '#FF9800'; // Orange - warning
    } else if (progressPercent >= 60) {
      return '#FFC107'; // Yellow - caution
    } else {
      return '#4CAF50'; // Green - good
    }
  }

  /**
   * Tempo-Status rendern
   */
  renderPaceStatus() {
    try {
      const element = this.elements.get('pace-status');
      if (!element) return;

      const userData = this.stateManager.getUserData();
      const progressPercent = userData.dailyPuffTarget > 0
        ? (userData.totalPuffsToday / userData.dailyPuffTarget) * 100
        : 0;

      const statusTexts = {
        excellent: 'Ausgezeichnet!',
        good: 'Auf Kurs',
        caution: 'Leicht voraus',
        warning: 'Voraus im Tempo',
        exceeded: 'Limit erreicht'
      };

      let status = 'good';
      if (progressPercent >= 100) {
        status = 'exceeded';
      } else if (progressPercent >= 80) {
        status = 'warning';
      } else if (progressPercent >= 60) {
        status = 'caution';
      } else if (progressPercent < 40) {
        status = 'excellent';
      }

      element.textContent = statusTexts[status] || 'Auf Kurs';
      element.className = `progress-status status-${status}`;
      
    } catch (error) {
      console.error('❌ Error rendering pace status:', error);
    }
  }

  /**
   * Letzte Puff-Info rendern
   */
  renderLastPuffInfo() {
    try {
      const userData = this.stateManager.getUserData();
      const lastPuffInfo = this.elements.get('last-puff-info');
      const lastPuffTime = this.elements.get('last-puff-time');

      if (!lastPuffInfo || !lastPuffTime) return;

      if (userData.lastPuffTime) {
        const lastTime = new Date(userData.lastPuffTime);
        const now = new Date();
        const diffMinutes = Math.floor((now - lastTime) / (1000 * 60));

        let timeText;
        if (diffMinutes < 1) {
          timeText = 'gerade eben';
        } else if (diffMinutes < 60) {
          timeText = `vor ${diffMinutes}m`;
        } else {
          const diffHours = Math.floor(diffMinutes / 60);
          const remainingMinutes = diffMinutes % 60;
          timeText = `vor ${diffHours}h ${remainingMinutes}m`;
        }

        lastPuffTime.textContent = timeText;
        lastPuffInfo.style.display = 'block';
      } else {
        lastPuffInfo.style.display = 'none';
      }
      
    } catch (error) {
      console.error('❌ Error rendering last puff info:', error);
    }
  }

  /**
   * Status-Nachricht rendern
   */
  renderStatusMessage() {
    try {
      const uiState = this.stateManager.getUIState();
      const element = this.elements.get('status-message');
      
      if (!element) return;

      if (uiState.showStatusMessage && uiState.statusMessage) {
        element.textContent = uiState.statusMessage;
        element.className = `status-message status-${uiState.statusType}`;
        element.style.display = 'block';
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
          this.hideStatusMessage();
        }, 5000);
      } else {
        element.style.display = 'none';
      }
      
    } catch (error) {
      console.error('❌ Error rendering status message:', error);
    }
  }

  /**
   * Status-Nachricht ausblenden
   */
  hideStatusMessage() {
    try {
      this.stateManager.updateState('ui.showStatusMessage', false);
      this.stateManager.updateState('ui.statusMessage', '');
      
    } catch (error) {
      console.error('❌ Error hiding status message:', error);
    }
  }

  /**
   * Status-Nachricht anzeigen
   */
  showStatusMessage(message, type = 'info') {
    try {
      this.stateManager.updateState('ui.statusMessage', message);
      this.stateManager.updateState('ui.statusType', type);
      this.stateManager.updateState('ui.showStatusMessage', true);
      
    } catch (error) {
      console.error('❌ Error showing status message:', error);
    }
  }

  /**
   * Responsive Elemente aktualisieren
   */
  updateResponsiveElements() {
    try {
      // Update touch targets for mobile
      const isMobile = window.innerWidth <= 768;
      
      document.body.classList.toggle('mobile-view', isMobile);
      document.body.classList.toggle('desktop-view', !isMobile);
      
    } catch (error) {
      console.error('❌ Error updating responsive elements:', error);
    }
  }

  /**
   * Eingabefelder zurücksetzen
   */
  resetInputs() {
    try {
      const puffInput = this.elements.get('puff-input');
      const deviceInput = this.elements.get('device-puff-input');
      
      if (puffInput) {
        puffInput.value = '1';
        puffInput.classList.remove('input-error');
      }
      
      if (deviceInput) {
        deviceInput.value = '';
        deviceInput.classList.remove('input-error');
      }
      
      // Clear any error messages
      document.querySelectorAll('.input-error-message').forEach(el => el.remove());
      
    } catch (error) {
      console.error('❌ Error resetting inputs:', error);
    }
  }

  /**
   * Loading-Zustand anzeigen/ausblenden
   */
  setLoading(isLoading) {
    try {
      this.stateManager.updateState('app.isLoading', isLoading);
      document.body.classList.toggle('loading', isLoading);
      
    } catch (error) {
      console.error('❌ Error setting loading state:', error);
    }
  }

  /**
   * Debounce-Hilfsfunktion
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * Element sicher abrufen
   */
  getElement(id) {
    if (this.elements.has(id)) {
      return this.elements.get(id);
    }
    
    const element = document.getElementById(id);
    if (element) {
      this.elements.set(id, element);
      return element;
    }
    
    console.warn(`⚠️ Element not found: ${id}`);
    return null;
  }

  /**
   * Alle gecachten Elemente aktualisieren
   */
  refreshElementCache() {
    this.elements.clear();
    this.cacheElements();
  }
}

// Export für ES6 Module
export default UIRenderer;

// Globale Verfügbarkeit für Legacy-Code
window.UIRenderer = UIRenderer;
