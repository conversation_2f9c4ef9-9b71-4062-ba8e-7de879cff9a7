/**
 * ByeVape State Manager
 * Zentrale Zustandsverwaltung für die ByeVape App
 */

class StateManager {
  constructor() {
    this.state = this.getDefaultState();
    this.listeners = new Map();
    this.isInitialized = false;
    
    console.log('🔧 StateManager initialized');
  }

  /**
   * Standard-Zustand der Anwendung
   */
  getDefaultState() {
    return {
      // User data
      userData: {
        // Existing fields (for backward compatibility)
        dailyVapingCount: 0,
        cessationSpeed: 'normal',
        startDate: null,
        currentDay: 1,
        breaksRemaining: 0,
        totalBreaks: 0,
        breaksTaken: 0,

        // New fields for reduction tracking
        trackingMode: 'breaks', // 'breaks' or 'reduction'
        baselineVapeCount: 0, // Original daily vaping count for reduction mode
        reductionTimeline: 60, // Days for reduction (30, 60, or 90)
        sleepDuration: 6, // Hours of sleep per day
        activeHours: 18, // Calculated: 24 - sleepDuration
        
        // Universal puff tracking fields
        totalPuffsToday: 0, // Total puffs taken today
        lastRecordedDevicePuffValue: 0, // Last device puff counter value
        puffTimes: [], // Array of puff timestamps
        lastPuffTime: null, // ISO string of last puff time
        dailyPuffTarget: 0, // Target puffs for today (calculated)
        averagePuffsPerBreak: 10, // Average puffs per vaping break
        
        // Legacy fields (for backward compatibility)
        dailyAllowance: 0,
        sessionsLogged: 0,
        sessionTimes: [],
        lastSessionTime: null,
        warningThreshold: 0.8
      },

      // App state
      app: {
        currentPage: 'tracker',
        isFirstLaunch: true,
        currentOnboardingStep: 1,
        isLoading: false,
        lastError: null
      },

      // UI state
      ui: {
        showStatusMessage: false,
        statusMessage: '',
        statusType: 'info',
        isTimerRunning: false,
        nextBreakTime: null
      },

      // Daily history
      dailyHistory: {}
    };
  }

  /**
   * Zustand initialisieren - lädt Daten aus localStorage
   */
  async initialize() {
    try {
      console.log('🔄 Initializing StateManager...');
      
      // Load user data
      await this.loadUserData();
      
      // Load daily history
      await this.loadDailyHistory();
      
      // Perform data migration if needed
      this.migrateUserData();
      
      // Validate loaded data
      if (!this.validateState()) {
        console.warn('⚠️ Invalid state detected, resetting to defaults');
        this.resetToDefaults();
      }
      
      this.isInitialized = true;
      this.notifyListeners('initialized', this.state);
      
      console.log('✅ StateManager initialized successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Error initializing StateManager:', error);
      this.resetToDefaults();
      this.isInitialized = true;
      return false;
    }
  }

  /**
   * Benutzerdaten aus localStorage laden
   */
  async loadUserData() {
    try {
      const savedData = localStorage.getItem('byevape-userdata');
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        this.state.userData = { ...this.state.userData, ...parsedData };
        console.log('📦 User data loaded from localStorage');
      }
    } catch (error) {
      console.error('❌ Error loading user data:', error);
      throw error;
    }
  }

  /**
   * Tägliche Historie aus localStorage laden
   */
  async loadDailyHistory() {
    try {
      const savedHistory = localStorage.getItem('byevape-daily-history');
      if (savedHistory) {
        this.state.dailyHistory = JSON.parse(savedHistory);
        console.log('📊 Daily history loaded from localStorage');
      }
    } catch (error) {
      console.error('❌ Error loading daily history:', error);
      // Don't throw - history is not critical
    }
  }

  /**
   * Datenmigration für neue Felder
   */
  migrateUserData() {
    let migrationNeeded = false;
    const userData = this.state.userData;

    // Set default tracking mode if not present
    if (!userData.trackingMode) {
      userData.trackingMode = 'breaks';
      migrationNeeded = true;
    }

    // Initialize puff tracking fields if not present
    if (userData.totalPuffsToday === undefined) {
      userData.totalPuffsToday = 0;
      userData.lastRecordedDevicePuffValue = 0;
      userData.puffTimes = [];
      userData.lastPuffTime = null;
      userData.dailyPuffTarget = 0;
      userData.averagePuffsPerBreak = 10;
      migrationNeeded = true;
    }

    // Set baseline vape count from existing dailyVapingCount if not set
    if (!userData.baselineVapeCount && userData.dailyVapingCount) {
      userData.baselineVapeCount = userData.dailyVapingCount;
      migrationNeeded = true;
    }

    // Set default sleep duration and calculate active hours
    if (!userData.sleepDuration) {
      userData.sleepDuration = 6;
      userData.activeHours = 18;
      migrationNeeded = true;
    }

    // Initialize session tracking arrays if not present
    if (!userData.sessionTimes) {
      userData.sessionTimes = [];
      userData.sessionsLogged = 0;
      userData.lastSessionTime = null;
      migrationNeeded = true;
    }

    // Set default warning threshold
    if (!userData.warningThreshold) {
      userData.warningThreshold = 0.8;
      migrationNeeded = true;
    }

    if (migrationNeeded) {
      console.log('🔄 User data migrated to new format');
      this.saveUserData();
    }
  }

  /**
   * Zustand validieren
   */
  validateState() {
    try {
      const userData = this.state.userData;
      
      // Check required fields
      const requiredFields = ['trackingMode', 'currentDay'];
      for (const field of requiredFields) {
        if (userData[field] === undefined || userData[field] === null) {
          console.error(`❌ Missing required field: ${field}`);
          return false;
        }
      }

      // Validate numeric ranges
      if (userData.currentDay < 1 || userData.currentDay > 365) {
        console.error('❌ Invalid current day value');
        return false;
      }

      if (userData.totalPuffsToday < 0) {
        console.error('❌ Invalid puff count');
        return false;
      }

      return true;
      
    } catch (error) {
      console.error('❌ Error validating state:', error);
      return false;
    }
  }

  /**
   * Auf Standardwerte zurücksetzen
   */
  resetToDefaults() {
    console.log('🔄 Resetting state to defaults');
    this.state = this.getDefaultState();
    this.saveUserData();
    this.saveDailyHistory();
  }

  /**
   * Zustand abrufen
   */
  getState() {
    return this.state;
  }

  /**
   * Benutzerdaten abrufen
   */
  getUserData() {
    return this.state.userData;
  }

  /**
   * App-Zustand abrufen
   */
  getAppState() {
    return this.state.app;
  }

  /**
   * UI-Zustand abrufen
   */
  getUIState() {
    return this.state.ui;
  }

  /**
   * Tägliche Historie abrufen
   */
  getDailyHistory() {
    return this.state.dailyHistory;
  }

  /**
   * Zustand aktualisieren
   */
  updateState(path, value) {
    try {
      const pathArray = path.split('.');
      let current = this.state;
      
      // Navigate to the parent of the target property
      for (let i = 0; i < pathArray.length - 1; i++) {
        if (!current[pathArray[i]]) {
          current[pathArray[i]] = {};
        }
        current = current[pathArray[i]];
      }
      
      // Set the value
      const lastKey = pathArray[pathArray.length - 1];
      const oldValue = current[lastKey];
      current[lastKey] = value;
      
      // Notify listeners
      this.notifyListeners('stateChanged', { path, oldValue, newValue: value });
      
      console.log(`🔄 State updated: ${path} = ${value}`);
      return true;
      
    } catch (error) {
      console.error('❌ Error updating state:', error);
      return false;
    }
  }

  /**
   * Benutzerdaten speichern mit verbesserter Fehlerbehandlung
   */
  async saveUserData() {
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        // Create backup before saving
        this.createDataBackup();

        // Validate data before saving
        if (!this.validateUserDataForSaving()) {
          throw new Error('Invalid user data detected before saving');
        }

        // Check localStorage availability and space
        this.checkLocalStorageAvailability();

        // Save with compression for large datasets
        const dataToSave = this.compressUserData(this.state.userData);
        localStorage.setItem('byevape-userdata', JSON.stringify(dataToSave));

        // Verify the save was successful
        const savedData = localStorage.getItem('byevape-userdata');
        if (!savedData) {
          throw new Error('Data was not saved successfully');
        }

        console.log('✅ User data saved to localStorage');
        this.notifyListeners('userDataSaved', this.state.userData);
        return true;

      } catch (error) {
        retryCount++;
        console.error(`❌ Error saving user data (attempt ${retryCount}/${maxRetries}):`, error);

        if (retryCount >= maxRetries) {
          // Try to restore from backup as last resort
          if (this.restoreFromBackup()) {
            console.log('🔄 Restored from backup after save failure');
          }

          // Notify about persistent save failure
          this.notifyListeners('saveError', { error, attempts: retryCount });
          return false;
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
      }
    }

    return false;
  }

  /**
   * Benutzerdaten für das Speichern validieren
   */
  validateUserDataForSaving() {
    try {
      const userData = this.state.userData;

      // Check for required fields
      if (typeof userData.currentDay !== 'number' || userData.currentDay < 1) {
        console.error('❌ Invalid currentDay for saving');
        return false;
      }

      if (typeof userData.totalPuffsToday !== 'number' || userData.totalPuffsToday < 0) {
        console.error('❌ Invalid totalPuffsToday for saving');
        return false;
      }

      // Check array fields
      if (!Array.isArray(userData.puffTimes)) {
        console.error('❌ Invalid puffTimes array for saving');
        return false;
      }

      return true;

    } catch (error) {
      console.error('❌ Error validating user data for saving:', error);
      return false;
    }
  }

  /**
   * localStorage-Verfügbarkeit und Speicherplatz prüfen
   */
  checkLocalStorageAvailability() {
    try {
      // Test if localStorage is available
      const testKey = 'byevape-test';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);

      // Check available space (rough estimate)
      const currentUsage = JSON.stringify(localStorage).length;
      const maxSize = 5 * 1024 * 1024; // 5MB typical limit

      if (currentUsage > maxSize * 0.9) {
        console.warn('⚠️ localStorage is nearly full, cleaning old data...');
        this.cleanupOldData();
      }

      return true;

    } catch (error) {
      console.error('❌ localStorage not available:', error);
      throw new Error('localStorage not available');
    }
  }

  /**
   * Benutzerdaten komprimieren (entfernt leere Arrays und null-Werte)
   */
  compressUserData(userData) {
    try {
      const compressed = { ...userData };

      // Remove empty arrays to save space
      Object.keys(compressed).forEach(key => {
        if (Array.isArray(compressed[key]) && compressed[key].length === 0) {
          compressed[key] = [];
        } else if (compressed[key] === null || compressed[key] === undefined) {
          delete compressed[key];
        }
      });

      // Limit puffTimes array to last 1000 entries to prevent excessive growth
      if (compressed.puffTimes && compressed.puffTimes.length > 1000) {
        compressed.puffTimes = compressed.puffTimes.slice(-1000);
        console.log('📦 Compressed puffTimes array to last 1000 entries');
      }

      return compressed;

    } catch (error) {
      console.error('❌ Error compressing user data:', error);
      return userData; // Return original if compression fails
    }
  }

  /**
   * Alte Daten bereinigen um Speicherplatz freizugeben
   */
  cleanupOldData() {
    try {
      // Remove old backup data
      const backupKeys = Object.keys(localStorage).filter(key =>
        key.startsWith('byevape-backup-') && key !== 'byevape-backup'
      );

      backupKeys.forEach(key => {
        localStorage.removeItem(key);
      });

      // Clean old daily history (already implemented in cleanOldHistoryData)
      this.cleanOldHistoryData();

      console.log('🧹 Cleaned up old data to free storage space');

    } catch (error) {
      console.error('❌ Error cleaning up old data:', error);
    }
  }

  /**
   * Tägliche Historie speichern
   */
  async saveDailyHistory() {
    try {
      // Clean old history data (keep only last 90 days)
      this.cleanOldHistoryData();
      
      localStorage.setItem('byevape-daily-history', JSON.stringify(this.state.dailyHistory));
      console.log('✅ Daily history saved to localStorage');
      return true;
      
    } catch (error) {
      console.error('❌ Error saving daily history:', error);
      return false;
    }
  }

  /**
   * Verbessertes Daten-Backup erstellen
   */
  createDataBackup() {
    try {
      const timestamp = new Date().toISOString();
      const backupData = {
        userData: { ...this.state.userData },
        dailyHistory: { ...this.state.dailyHistory },
        timestamp: timestamp,
        version: '2.0',
        checksum: this.calculateChecksum(this.state.userData)
      };

      // Create primary backup
      localStorage.setItem('byevape-backup', JSON.stringify(backupData));

      // Create rotating backups (keep last 3)
      this.createRotatingBackup(backupData, timestamp);

      console.log('📦 Enhanced data backup created');

    } catch (error) {
      console.error('❌ Error creating backup:', error);
    }
  }

  /**
   * Rotierende Backups erstellen
   */
  createRotatingBackup(backupData, timestamp) {
    try {
      const backupKey = `byevape-backup-${timestamp.split('T')[0]}`;

      // Save today's backup
      localStorage.setItem(backupKey, JSON.stringify(backupData));

      // Clean old rotating backups (keep only last 3 days)
      const allKeys = Object.keys(localStorage);
      const backupKeys = allKeys
        .filter(key => key.startsWith('byevape-backup-'))
        .sort()
        .reverse();

      // Remove backups older than 3 days
      if (backupKeys.length > 3) {
        backupKeys.slice(3).forEach(key => {
          localStorage.removeItem(key);
        });
      }

    } catch (error) {
      console.error('❌ Error creating rotating backup:', error);
    }
  }

  /**
   * Checksum für Datenintegrität berechnen
   */
  calculateChecksum(data) {
    try {
      const str = JSON.stringify(data);
      let hash = 0;

      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }

      return hash.toString(16);

    } catch (error) {
      console.error('❌ Error calculating checksum:', error);
      return null;
    }
  }

  /**
   * Verbessertes Wiederherstellen aus Backup
   */
  restoreFromBackup() {
    const backupSources = [
      'byevape-backup', // Primary backup
      ...this.getRotatingBackupKeys() // Rotating backups
    ];

    for (const backupKey of backupSources) {
      try {
        const backupData = localStorage.getItem(backupKey);
        if (!backupData) continue;

        const backup = JSON.parse(backupData);

        // Validate backup integrity
        if (!this.validateBackup(backup)) {
          console.warn(`⚠️ Invalid backup found: ${backupKey}`);
          continue;
        }

        // Restore data
        this.state.userData = backup.userData;
        this.state.dailyHistory = backup.dailyHistory || {};

        // Verify restored data
        if (this.validateState()) {
          console.log(`🔄 Data restored from backup: ${backupKey}`);
          this.notifyListeners('dataRestored', { source: backupKey, timestamp: backup.timestamp });
          return true;
        } else {
          console.warn(`⚠️ Restored data failed validation from: ${backupKey}`);
        }

      } catch (error) {
        console.error(`❌ Error restoring from backup ${backupKey}:`, error);
        continue;
      }
    }

    console.error('❌ No valid backup found for restoration');
    return false;
  }

  /**
   * Rotierende Backup-Schlüssel abrufen
   */
  getRotatingBackupKeys() {
    try {
      return Object.keys(localStorage)
        .filter(key => key.startsWith('byevape-backup-'))
        .sort()
        .reverse(); // Most recent first

    } catch (error) {
      console.error('❌ Error getting rotating backup keys:', error);
      return [];
    }
  }

  /**
   * Backup-Integrität validieren
   */
  validateBackup(backup) {
    try {
      // Check required fields
      if (!backup.userData || !backup.timestamp) {
        return false;
      }

      // Check version compatibility
      if (backup.version && backup.version !== '2.0') {
        console.warn(`⚠️ Backup version mismatch: ${backup.version}`);
      }

      // Verify checksum if available
      if (backup.checksum) {
        const calculatedChecksum = this.calculateChecksum(backup.userData);
        if (calculatedChecksum !== backup.checksum) {
          console.warn('⚠️ Backup checksum mismatch - data may be corrupted');
          return false;
        }
      }

      // Basic data structure validation
      const userData = backup.userData;
      if (typeof userData.currentDay !== 'number' || userData.currentDay < 1) {
        return false;
      }

      return true;

    } catch (error) {
      console.error('❌ Error validating backup:', error);
      return false;
    }
  }

  /**
   * Datenexport für Benutzer
   */
  exportUserData() {
    try {
      const exportData = {
        userData: this.state.userData,
        dailyHistory: this.state.dailyHistory,
        exportDate: new Date().toISOString(),
        version: '2.0',
        checksum: this.calculateChecksum(this.state.userData)
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `byevape-export-${new Date().toISOString().split('T')[0]}.json`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);

      console.log('📤 User data exported successfully');
      return true;

    } catch (error) {
      console.error('❌ Error exporting user data:', error);
      return false;
    }
  }

  /**
   * Datenimport für Benutzer
   */
  async importUserData(file) {
    try {
      const text = await file.text();
      const importData = JSON.parse(text);

      // Validate import data
      if (!this.validateBackup(importData)) {
        throw new Error('Invalid import data format');
      }

      // Create backup before import
      this.createDataBackup();

      // Import data
      this.state.userData = importData.userData;
      this.state.dailyHistory = importData.dailyHistory || {};

      // Save imported data
      await this.saveUserData();
      await this.saveDailyHistory();

      console.log('📥 User data imported successfully');
      this.notifyListeners('dataImported', { timestamp: importData.exportDate });

      return true;

    } catch (error) {
      console.error('❌ Error importing user data:', error);

      // Try to restore from backup if import failed
      this.restoreFromBackup();

      return false;
    }
  }

  /**
   * Alte Historie-Daten bereinigen
   */
  cleanOldHistoryData() {
    const today = new Date();
    const ninetyDaysAgo = new Date(today.getTime() - (90 * 24 * 60 * 60 * 1000));
    const cutoffDate = ninetyDaysAgo.toISOString().split('T')[0];

    let cleanedCount = 0;
    Object.keys(this.state.dailyHistory).forEach(dateStr => {
      if (dateStr < cutoffDate) {
        delete this.state.dailyHistory[dateStr];
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned ${cleanedCount} old history entries`);
    }
  }

  /**
   * Event-Listener hinzufügen
   */
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * Event-Listener entfernen
   */
  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Listener benachrichtigen
   */
  notifyListeners(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ Error in event listener for ${event}:`, error);
        }
      });
    }
  }
}

// Export für ES6 Module
export default StateManager;

// Globale Verfügbarkeit für Legacy-Code
window.StateManager = StateManager;
