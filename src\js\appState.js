/**
 * ByeVape Central App State
 * Globale Zustandsverwaltung und Koordination aller Module
 */

class AppState {
  constructor() {
    this.logger = window.logger;
    this.errorHandler = null;
    this.stateManager = null;
    this.uiRenderer = null;
    this.trackerLogic = null;
    this.isInitialized = false;
    this.initializationPromise = null;

    this.logger.info('🏗️ AppState constructor called');
  }

  /**
   * App-Zustand initialisieren
   */
  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._performInitialization();
    return this.initializationPromise;
  }

  /**
   * Interne Initialisierung durchführen
   */
  async _performInitialization() {
    const perf = this.logger.startPerformance('AppState initialization');

    try {
      this.logger.info('🔄 Initializing AppState...');

      // Initialize ErrorHandler first
      this.errorHandler = new ErrorHandler(this.logger);
      this.errorHandler.initialize();

      // Initialize StateManager
      this.stateManager = new StateManager();
      await this.stateManager.initialize();

      // Initialize UIRenderer
      this.uiRenderer = new UIRenderer(this.stateManager);
      await this.uiRenderer.initialize();

      // Initialize TrackerLogic
      this.trackerLogic = new TrackerLogic(this.stateManager);
      await this.trackerLogic.initialize();

      // Setup cross-module communication
      this.setupModuleCommunication();

      // Perform initial data checks
      await this.performInitialChecks();

      this.isInitialized = true;
      this.logger.info('✅ AppState initialized successfully');

      // Notify about successful initialization
      this.stateManager.notifyListeners('appInitialized', {
        timestamp: new Date().toISOString()
      });

      return true;

    } catch (error) {
      this.logger.error('❌ Error initializing AppState', error);
      this.handleInitializationError(error);
      return false;
    } finally {
      perf.end();
    }
  }

  /**
   * Module-übergreifende Kommunikation einrichten
   */
  setupModuleCommunication() {
    try {
      // StateManager events
      this.stateManager.addEventListener('stateChanged', (data) => {
        this.handleStateChange(data);
      });

      this.stateManager.addEventListener('saveError', (data) => {
        this.handleSaveError(data);
      });

      this.stateManager.addEventListener('dataRestored', (data) => {
        this.handleDataRestored(data);
      });

      // Setup periodic tasks
      this.setupPeriodicTasks();
      
      console.log('🔗 Module communication setup complete');
      
    } catch (error) {
      console.error('❌ Error setting up module communication:', error);
    }
  }

  /**
   * Periodische Aufgaben einrichten
   */
  setupPeriodicTasks() {
    // Update UI every 30 seconds
    setInterval(() => {
      if (this.isInitialized) {
        this.uiRenderer.renderLastPuffInfo();
      }
    }, 30000);

    // Save data every 5 minutes
    setInterval(() => {
      if (this.isInitialized) {
        this.stateManager.saveUserData();
      }
    }, 5 * 60000);

    // Check for daily reset every hour
    setInterval(() => {
      if (this.isInitialized) {
        this.trackerLogic.checkDailyReset();
      }
    }, 60 * 60000);
  }

  /**
   * Anfängliche Überprüfungen durchführen
   */
  async performInitialChecks() {
    try {
      // Check if daily reset is needed
      this.trackerLogic.checkDailyReset();
      
      // Update daily puff target
      this.trackerLogic.updateDailyPuffTarget();
      
      // Render initial UI
      await this.uiRenderer.renderAll();
      
      console.log('✅ Initial checks completed');
      
    } catch (error) {
      console.error('❌ Error performing initial checks:', error);
    }
  }

  /**
   * Zustandsänderungen behandeln
   */
  handleStateChange(data) {
    try {
      const { path, newValue, oldValue } = data;
      
      // Log significant changes
      if (path.includes('totalPuffsToday') || path.includes('dailyPuffTarget')) {
        console.log(`📊 State change: ${path} = ${newValue} (was: ${oldValue})`);
      }
      
      // Trigger specific actions based on state changes
      if (path === 'userData.totalPuffsToday') {
        this.handlePuffCountChange(newValue, oldValue);
      } else if (path === 'userData.dailyPuffTarget') {
        this.handleTargetChange(newValue, oldValue);
      }
      
    } catch (error) {
      console.error('❌ Error handling state change:', error);
    }
  }

  /**
   * Puff-Anzahl-Änderung behandeln
   */
  handlePuffCountChange(newCount, oldCount) {
    try {
      // Check if we need to show warnings
      const paceCheck = this.trackerLogic.checkConsumptionPace();
      if (paceCheck.status !== 'ok' && paceCheck.status !== 'good') {
        this.uiRenderer.showStatusMessage(paceCheck.message, paceCheck.status);
      }
      
      // Update statistics
      const stats = this.trackerLogic.calculatePuffStatistics();
      if (stats) {
        this.stateManager.updateState('app.currentStats', stats);
      }
      
    } catch (error) {
      console.error('❌ Error handling puff count change:', error);
    }
  }

  /**
   * Ziel-Änderung behandeln
   */
  handleTargetChange(newTarget, oldTarget) {
    try {
      console.log(`🎯 Daily target changed: ${oldTarget} → ${newTarget}`);
      
      // Update UI to reflect new target
      this.uiRenderer.renderProgress();
      
    } catch (error) {
      console.error('❌ Error handling target change:', error);
    }
  }

  /**
   * Speicherfehler behandeln
   */
  handleSaveError(data) {
    try {
      console.error('💾 Save error occurred:', data);
      
      this.uiRenderer.showStatusMessage(
        'Fehler beim Speichern der Daten. Ihre Eingaben könnten verloren gehen.',
        'error'
      );
      
      // Try alternative save methods or notify user
      this.attemptRecovery();
      
    } catch (error) {
      console.error('❌ Error handling save error:', error);
    }
  }

  /**
   * Datenwiederherstellung behandeln
   */
  handleDataRestored(data) {
    try {
      console.log('🔄 Data restored from backup:', data);
      
      this.uiRenderer.showStatusMessage(
        'Daten wurden aus einem Backup wiederhergestellt.',
        'info'
      );
      
      // Refresh UI with restored data
      this.uiRenderer.renderAll();
      
    } catch (error) {
      console.error('❌ Error handling data restoration:', error);
    }
  }

  /**
   * Initialisierungsfehler behandeln
   */
  handleInitializationError(error) {
    try {
      console.error('🚨 Critical initialization error:', error);
      
      // Try to show error to user
      const errorMessage = document.createElement('div');
      errorMessage.className = 'critical-error';
      errorMessage.innerHTML = `
        <h3>Initialisierungsfehler</h3>
        <p>Die App konnte nicht ordnungsgemäß gestartet werden.</p>
        <p>Bitte laden Sie die Seite neu oder kontaktieren Sie den Support.</p>
        <button onclick="location.reload()">Seite neu laden</button>
      `;
      
      document.body.appendChild(errorMessage);
      
    } catch (displayError) {
      console.error('❌ Could not display initialization error:', displayError);
    }
  }

  /**
   * Wiederherstellungsversuch
   */
  async attemptRecovery() {
    try {
      console.log('🔄 Attempting data recovery...');
      
      // Try to restore from backup
      if (this.stateManager.restoreFromBackup()) {
        this.uiRenderer.showStatusMessage('Daten erfolgreich wiederhergestellt.', 'success');
        return true;
      }
      
      // If backup restoration fails, try to save current state again
      const saveSuccess = await this.stateManager.saveUserData();
      if (saveSuccess) {
        this.uiRenderer.showStatusMessage('Daten erfolgreich gespeichert.', 'success');
        return true;
      }
      
      // Last resort: export data for user
      this.stateManager.exportUserData();
      this.uiRenderer.showStatusMessage(
        'Automatischer Download Ihrer Daten gestartet. Bitte sichern Sie diese Datei.',
        'warning'
      );
      
      return false;
      
    } catch (error) {
      console.error('❌ Error during recovery attempt:', error);
      return false;
    }
  }

  // ===================================
  // PUBLIC API METHODS
  // ===================================

  /**
   * Puffs protokollieren
   */
  async logPuffs(count) {
    if (!this.isInitialized) {
      this.logger.error('❌ AppState not initialized');
      return false;
    }

    return await this.errorHandler.retry(async () => {
      this.logger.userAction('logPuffs', { count });

      // Validate input
      const validation = this.validatePuffInput(count);
      if (!validation.isValid) {
        this.uiRenderer.showStatusMessage(validation.error, 'warning');
        throw new Error(`Invalid input: ${validation.error}`);
      }

      // Log puffs
      const userData = this.stateManager.getUserData();
      const newTotal = userData.totalPuffsToday + validation.value;

      // Update state
      this.stateManager.updateState('userData.totalPuffsToday', newTotal);
      this.stateManager.updateState('userData.lastPuffTime', new Date().toISOString());

      // Add to puff times array
      const puffTimes = [...userData.puffTimes];
      for (let i = 0; i < validation.value; i++) {
        puffTimes.push(new Date().toISOString());
      }
      this.stateManager.updateState('userData.puffTimes', puffTimes);

      // Save data
      await this.stateManager.saveUserData();

      this.logger.info(`📝 Logged ${validation.value} puff(s). Total: ${newTotal}`);
      return true;

    }, { operation: 'logPuffs', count }).then(result => {
      if (!result.success) {
        this.logger.error('❌ Error logging puffs', result.error);
        this.uiRenderer.showStatusMessage('Fehler beim Protokollieren der Puffs.', 'error');
        return false;
      }
      return result.result;
    });
  }

  /**
   * Gerätesynchronisation
   */
  async syncDevicePuffs(deviceValue) {
    if (!this.isInitialized) {
      console.error('❌ AppState not initialized');
      return false;
    }

    try {
      const userData = this.stateManager.getUserData();
      const lastValue = userData.lastRecordedDevicePuffValue || 0;
      
      if (deviceValue <= lastValue) {
        this.uiRenderer.showStatusMessage(
          `Gerätewert muss höher als ${lastValue} sein.`,
          'warning'
        );
        return false;
      }

      const puffDelta = deviceValue - lastValue;
      
      // Update device value
      this.stateManager.updateState('userData.lastRecordedDevicePuffValue', deviceValue);
      
      // Log the delta puffs
      return await this.logPuffs(puffDelta);
      
    } catch (error) {
      console.error('❌ Error syncing device puffs:', error);
      this.uiRenderer.showStatusMessage('Fehler bei der Gerätesynchronisation.', 'error');
      return false;
    }
  }

  /**
   * Letzten Puff rückgängig machen
   */
  async undoLastPuff() {
    if (!this.isInitialized) {
      console.error('❌ AppState not initialized');
      return false;
    }

    try {
      const userData = this.stateManager.getUserData();
      
      if (userData.totalPuffsToday <= 0) {
        this.uiRenderer.showStatusMessage('Keine Puffs zum Rückgängigmachen vorhanden.', 'info');
        return false;
      }

      // Update counts
      this.stateManager.updateState('userData.totalPuffsToday', userData.totalPuffsToday - 1);
      
      // Remove last puff time
      const puffTimes = [...userData.puffTimes];
      puffTimes.pop();
      this.stateManager.updateState('userData.puffTimes', puffTimes);
      
      // Update last puff time
      const lastPuffTime = puffTimes.length > 0 ? puffTimes[puffTimes.length - 1] : null;
      this.stateManager.updateState('userData.lastPuffTime', lastPuffTime);
      
      // Save data
      await this.stateManager.saveUserData();
      
      this.uiRenderer.showStatusMessage('Letzter Puff wurde rückgängig gemacht.', 'success');
      console.log('↩️ Last puff undone');
      return true;
      
    } catch (error) {
      console.error('❌ Error undoing last puff:', error);
      this.uiRenderer.showStatusMessage('Fehler beim Rückgängigmachen.', 'error');
      return false;
    }
  }

  /**
   * Eingabe validieren
   */
  validatePuffInput(input) {
    try {
      const numValue = parseInt(input);
      
      if (isNaN(numValue)) {
        return { isValid: false, error: 'Bitte geben Sie eine gültige Zahl ein.' };
      }
      
      if (numValue < 1) {
        return { isValid: false, error: 'Mindestens 1 Puff muss eingegeben werden.' };
      }
      
      if (numValue > 50) {
        return { isValid: false, error: 'Maximal 50 Puffs können auf einmal eingegeben werden.' };
      }
      
      return { isValid: true, value: numValue };
      
    } catch (error) {
      return { isValid: false, error: 'Fehler bei der Eingabevalidierung.' };
    }
  }

  /**
   * App-Status abrufen
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      hasStateManager: !!this.stateManager,
      hasUIRenderer: !!this.uiRenderer,
      hasTrackerLogic: !!this.trackerLogic
    };
  }

  /**
   * Statistiken abrufen
   */
  getStatistics() {
    if (!this.isInitialized || !this.trackerLogic) {
      return null;
    }
    
    return this.trackerLogic.calculatePuffStatistics();
  }
}

// Globale App-Instanz erstellen
const appState = new AppState();

// Export für ES6 Module
export default appState;

// Globale Verfügbarkeit
window.appState = appState;
