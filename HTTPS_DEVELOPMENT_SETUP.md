# Development Server Setup Guide

## Overview
The ByeVape app supports both HTTP and HTTPS development servers to provide flexible local testing options before deploying to Android Studio via Capacitor.

## 🚀 Quick Start (Recommended for Local Development)

### HTTP Development Server (No SSL Issues)
```bash
npm start
```
- **URL**: http://localhost:3000/
- **Network**: http://[your-ip]:3000/
- **Purpose**: Daily development with hot reload
- **Benefits**: No certificate issues, faster startup, reliable

### HTTPS Development Server (For Final Testing)
```bash
npm run start:https
```
- **URL**: https://localhost:3000/
- **Network**: https://[your-ip]:3000/
- **Purpose**: Final testing before deployment
- **Benefits**: Matches production environment, enables PWA features

## Configuration Details

### Vite Configuration (`vite.config.ts`)
```typescript
import { defineConfig } from 'vite';

// Check if HTTPS mode is requested via environment variable
const useHttps = process.env.VITE_HTTPS === 'true';

export default defineConfig({
  root: './src',
  build: {
    outDir: '../dist',
    minify: false,
    emptyOutDir: true,
  },
  server: {
    https: useHttps,
    host: true, // Allow external connections
    port: 3000,
    open: false // Don't auto-open browser
  },
  preview: {
    https: useHttps,
    host: true,
    port: 3001,
    open: false
  }
});
```

## Available Commands

### HTTP Development (Recommended)
```bash
npm start
```
- **URL**: http://localhost:3000/
- **Network**: http://[your-ip]:3000/
- **Purpose**: Daily development with hot reload
- **No SSL issues**: Works reliably in all browsers

### HTTPS Development (When Needed)
```bash
npm run start:https
```
- **URL**: https://localhost:3000/
- **Network**: https://[your-ip]:3000/
- **Purpose**: Testing with secure context
- **Certificate**: Self-signed (browser will show security warning)

### Build & Preview
```bash
npm run build
npm run preview        # HTTP preview
npm run preview:https  # HTTPS preview
```
- **HTTP Preview**: http://localhost:3001/
- **HTTPS Preview**: https://localhost:3001/
- **Purpose**: Test production build locally

## Browser Setup Instructions

### 1. Accept Self-Signed Certificate
When first accessing the HTTPS URLs, your browser will show a security warning:

**Chrome/Edge:**
1. Click "Advanced"
2. Click "Proceed to localhost (unsafe)"

**Firefox:**
1. Click "Advanced"
2. Click "Accept the Risk and Continue"

**Safari:**
1. Click "Show Details"
2. Click "visit this website"
3. Click "Visit Website" in the popup

### 2. Bookmark for Easy Access
After accepting the certificate, bookmark the URLs for quick access:
- Development: https://localhost:3000/
- Preview: https://localhost:3001/

## Network Access

### Local Network Testing
The servers are configured with `host: true`, allowing access from other devices on your network:

1. Find your computer's IP address:
   ```bash
   # Windows (PowerShell)
   ipconfig | findstr IPv4
   
   # The server will display the network URL automatically
   ```

2. Access from mobile devices or other computers:
   - Development: https://[your-ip]:3000/
   - Preview: https://[your-ip]:3001/

### Security Considerations
- Self-signed certificates will show warnings on all devices
- Only use for development/testing purposes
- Network access is limited to your local network

## Testing Workflow

### 1. Development Testing
```bash
# Start development server
npm start

# Open browser to https://localhost:3000/
# Accept certificate warning
# Test app functionality
```

### 2. Production Build Testing
```bash
# Build the app
npm run build

# Start preview server
npm run preview

# Open browser to https://localhost:3001/
# Test production build
```

### 3. Mobile Device Testing
```bash
# Start development server
npm start

# Note the Network URL from console output
# Access from mobile browser: https://[ip]:3000/
# Accept certificate warning on mobile
```

## Integration with Capacitor

### Before Android Deployment
1. Test thoroughly using HTTPS development server
2. Verify all features work correctly
3. Build production version: `npm run build`
4. Deploy to Android: `npx cap sync android`

### Benefits of HTTPS Testing
- **Security Context**: Tests app in secure context like production
- **Service Workers**: Enables testing of PWA features
- **Camera/Sensors**: Some device APIs require HTTPS
- **Real Conditions**: Mimics production environment

## Troubleshooting

### SSL/TLS Cipher Overlap Error
If you encounter "SSL_ERROR_NO_CYPHER_OVERLAP" or similar HTTPS errors:

**Solution 1: Use HTTP Development Server (Recommended)**
```bash
npm start  # Uses HTTP instead of HTTPS
```
This avoids all SSL issues and is perfect for daily development.

**Solution 2: Try Different Browser**
- Chrome/Edge: Often more compatible with self-signed certificates
- Firefox: May have stricter SSL requirements
- Safari: Try enabling "Allow invalid certificates for resources loaded from localhost"

**Solution 3: Clear SSL State**
```bash
# Windows: Clear SSL state in Internet Options
# Chrome: Go to chrome://settings/privacy → Clear browsing data → Advanced → Cached images and files
```

### Port Already in Use
If ports 3000 or 3001 are busy:
```bash
# Kill processes using the ports (Windows PowerShell)
netstat -ano | findstr :3000
taskkill /PID [process-id] /F
```

### Certificate Issues
- Clear browser cache and cookies for localhost
- Try incognito/private browsing mode
- Restart browser after accepting certificate
- Use HTTP development server to avoid certificate issues entirely

### Network Access Issues
- Check firewall settings
- Ensure devices are on same network
- Verify IP address is correct

## Next Steps

After successful HTTPS testing:
1. Deploy to Android Studio via Capacitor
2. Test on physical Android devices
3. Submit to app stores if ready

## Support

For issues with HTTPS setup:
1. Check browser console for errors
2. Verify vite.config.ts configuration
3. Test with different browsers
4. Check network connectivity
